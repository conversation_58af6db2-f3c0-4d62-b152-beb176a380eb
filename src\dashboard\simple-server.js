#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import { SITES } from '../config/sites.js';
import { readFileSync, existsSync, readdirSync, statSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Simplified Dashboard Server for Web Scraper Tool
 */
class SimpleDashboardServer {
  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    this.activeJobs = new Map();
    this.jobCounter = 0;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, 'public')));
  }

  setupRoutes() {
    // API Routes
    this.app.get('/api/sites', this.getSites.bind(this));
    this.app.post('/api/scrape/:site', this.startScraping.bind(this));
    this.app.get('/api/jobs', this.getJobs.bind(this));
    this.app.get('/api/jobs/:jobId', this.getJobStatus.bind(this));
    this.app.delete('/api/jobs/:jobId', this.cancelJob.bind(this));
    this.app.get('/api/results', this.getResults.bind(this));
    this.app.get('/api/results/:filename', this.downloadResult.bind(this));

    // Serve dashboard
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws) => {
      console.log('Dashboard client connected');
      
      // Send current job status
      ws.send(JSON.stringify({
        type: 'jobs_update',
        data: Array.from(this.activeJobs.values())
      }));

      ws.on('close', () => {
        console.log('Dashboard client disconnected');
      });
    });
  }

  // API Handlers
  getSites(req, res) {
    const sites = Object.entries(SITES).map(([key, config]) => ({
      id: key,
      name: config.name,
      url: config.url,
      type: config.type,
      strategy: config.strategy
    }));
    
    res.json({ sites });
  }

  async startScraping(req, res) {
    const { site } = req.params;
    const siteConfig = SITES[site];
    
    if (!siteConfig) {
      return res.status(404).json({ 
        error: `Site '${site}' not found. Available sites: ${Object.keys(SITES).join(', ')}` 
      });
    }

    const jobId = `job_${++this.jobCounter}_${Date.now()}`;
    const job = {
      id: jobId,
      site,
      siteName: siteConfig.name,
      status: 'starting',
      startTime: new Date().toISOString(),
      progress: 0,
      logs: [`Starting scraping job for ${siteConfig.name}`]
    };

    this.activeJobs.set(jobId, job);
    this.broadcastJobUpdate();

    // Simulate scraping job (replace with actual scraper later)
    this.simulateScrapingJob(jobId, siteConfig);

    res.json({ jobId, message: `Started scraping ${siteConfig.name}` });
  }

  async simulateScrapingJob(jobId, siteConfig) {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    try {
      job.status = 'running';
      job.logs.push(`Connecting to ${siteConfig.url}`);
      this.broadcastJobUpdate();

      // Simulate progress
      for (let i = 0; i <= 100; i += 20) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        job.progress = i;
        job.logs.push(`Progress: ${i}%`);
        this.broadcastJobUpdate();
      }
      
      job.status = 'completed';
      job.progress = 100;
      job.endTime = new Date().toISOString();
      job.result = {
        message: `Simulated scraping completed for ${siteConfig.name}`,
        itemsFound: Math.floor(Math.random() * 100) + 1
      };
      job.logs.push(`Completed scraping for ${siteConfig.name}`);
      
      this.broadcastJobUpdate();
      
    } catch (error) {
      job.status = 'failed';
      job.error = error.message;
      job.endTime = new Date().toISOString();
      job.logs.push(`Error: ${error.message}`);
      this.broadcastJobUpdate();
    }
  }

  getJobs(req, res) {
    const jobs = Array.from(this.activeJobs.values());
    res.json({ jobs });
  }

  getJobStatus(req, res) {
    const { jobId } = req.params;
    const job = this.activeJobs.get(jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }
    
    res.json({ job });
  }

  cancelJob(req, res) {
    const { jobId } = req.params;
    const job = this.activeJobs.get(jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }
    
    if (job.status === 'running') {
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      job.logs.push('Job cancelled by user');
      this.broadcastJobUpdate();
    }
    
    res.json({ message: 'Job cancelled' });
  }

  getResults(req, res) {
    try {
      const resultsDir = path.join(process.cwd(), 'results');
      if (!existsSync(resultsDir)) {
        return res.json({ files: [] });
      }

      const files = readdirSync(resultsDir)
        .filter(file => file.endsWith('.json'))
        .map(file => {
          const filePath = path.join(resultsDir, file);
          const stats = statSync(filePath);
          return {
            name: file,
            size: stats.size,
            modified: stats.mtime.toISOString()
          };
        });

      res.json({ files });
    } catch (error) {
      res.status(500).json({ error: 'Failed to read results directory' });
    }
  }

  downloadResult(req, res) {
    const { filename } = req.params;
    const filePath = path.join(process.cwd(), 'results', filename);
    
    if (!existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }
    
    res.download(filePath);
  }

  broadcastJobUpdate() {
    const jobs = Array.from(this.activeJobs.values());
    const message = JSON.stringify({
      type: 'jobs_update',
      data: jobs
    });

    this.wss.clients.forEach(client => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(message);
      }
    });
  }

  start(port = 3000) {
    try {
      console.log('Starting simplified dashboard server...');
      
      this.server.listen(port, () => {
        console.log(`🚀 Dashboard server running at http://localhost:${port}`);
        console.log('Available endpoints:');
        console.log('  - Dashboard: http://localhost:' + port);
        console.log('  - API: http://localhost:' + port + '/api');
      });
    } catch (error) {
      console.error('Failed to start dashboard server:', error);
      process.exit(1);
    }
  }
}

// Start server if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const port = process.env.PORT || 3000;
  const server = new SimpleDashboardServer();
  server.start(port);
}

export { SimpleDashboardServer };
