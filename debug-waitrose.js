#!/usr/bin/env node

import { WaitroseScraper } from './src/scrapers/WaitroseScraper.js';
import { SITES } from './src/config/sites.js';
import { Logger } from './src/utils/logger.js';

/**
 * Debug script to investigate Waitrose scraping issues
 */
async function debugWaitrose() {
  const scraper = new WaitroseScraper(SITES.waitrose);

  try {
    Logger.info('=== WAITROSE DEBUG SESSION ===');
    Logger.info('🔍 Testing the EXACT same method the dashboard uses...');

    // Test the exact same method that the dashboard calls
    Logger.info('🚀 Calling scraper.scrapeProducts() - same as dashboard...');
    const result = await scraper.scrapeProducts(['Fresh & Chilled']);

    Logger.info('📊 Scraping result:', JSON.stringify({
      success: result.success,
      totalProducts: result.totalProducts,
      resultsPath: result.resultsPath
    }, null, 2));

    if (result.products && result.products.length > 0) {
      Logger.success(`✅ SUCCESS! Found ${result.products.length} products`);
      Logger.info('📦 Sample products:');
      result.products.slice(0, 3).forEach((product, index) => {
        Logger.info(`${index + 1}. ${product.name} - ${product.price}`);
      });
    } else {
      Logger.error('❌ FAILED! No products found');
    }

    Logger.info('=== DEBUG SESSION COMPLETE ===');

  } catch (error) {
    Logger.error('Debug session failed:', error);
  } finally {
    await scraper.close();
  }
}

// Run the debug session
debugWaitrose().catch(console.error);
