import { WaitroseScraper } from './src/scrapers/WaitroseScraper.js';
import { Logger } from './src/utils/Logger.js';
import { SITES } from './src/config/sites.js';

async function testWaitroseScraper() {
  console.log('🔍 Testing Waitrose scraper integration...');
  
  try {
    // Get site configuration
    const siteConfig = SITES.waitrose;
    console.log('📋 Site config:', siteConfig);

    // Create scraper instance
    const scraper = new WaitroseScraper(siteConfig);
    
    // Set up progress tracking
    scraper.getEventEmitter().on('progress', (progress) => {
      console.log(`📊 Progress: ${progress.stage} - ${progress.percentage}% - ${progress.message}`);
      if (progress.productsFound !== undefined) {
        console.log(`   Products found so far: ${progress.productsFound}`);
      }
    });
    
    console.log('🚀 Starting scrapeProducts with Fresh & Chilled category...');
    
    // Call scrapeProducts method (same as ScraperManager calls)
    const result = await scraper.scrapeProducts(['Fresh & Chilled']);
    
    console.log('✅ Scraping completed!');
    console.log('📊 Final result:', {
      success: result.success,
      totalProducts: result.totalProducts,
      resultsPath: result.resultsPath,
      productsLength: result.products ? result.products.length : 'undefined'
    });
    
    if (result.products && result.products.length > 0) {
      console.log('🛍️ Sample products:');
      result.products.slice(0, 3).forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} - ${product.price}`);
      });
    } else {
      console.log('❌ No products found in result');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testWaitroseScraper();
