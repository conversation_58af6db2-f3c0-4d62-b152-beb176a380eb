{"name": "stagehand-browserbase-scraper-poc", "version": "1.0.0", "description": "POC for testing Stagehand/Browserbase automation for web scraping", "main": "src/index.js", "type": "module", "scripts": {"setup": "node setup.js", "start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node src/test.js", "test:site": "node src/testSite.js", "dashboard": "node minimal-dashboard.js", "dashboard:dev": "node --watch minimal-dashboard.js"}, "keywords": ["stagehand", "browserbase", "web-scraping", "automation", "poc"], "author": "", "license": "MIT", "dependencies": {"@browserbasehq/stagehand": "^1.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/node": "^20.0.0"}}