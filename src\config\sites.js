/**
 * Configuration for target websites
 */
export const SITES = {
  waitrose: {
    name: 'Wait<PERSON>',
    url: 'https://www.waitrose.com/',
    type: 'grocery',
    categories: [
      'Fresh & Chilled',
      'Frozen',
      'Bakery',
      'Food Cupboard',
      'Beer, Wine & Spirits',
      'Tea, Coffee & Soft Drinks'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  tesco: {
    name: 'Tesco',
    url: 'https://www.tesco.com/groceries/en-GB',
    type: 'grocery',
    categories: [
      'Fresh Food',
      'Bakery',
      'Frozen Food',
      'Food Cupboard',
      'Drinks',
      'Baby',
      'Health & Beauty',
      'Household',
      'Pet'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  hamleys: {
    name: '<PERSON><PERSON><PERSON>',
    url: 'https://www.hamleys.com/',
    type: 'toys',
    categories: [
      'Action Figures & Collectibles',
      'Arts & Crafts',
      'Baby & Preschool',
      'Building & Construction',
      'Dolls & Accessories',
      'Games & Puzzles',
      'Outdoor & Sports',
      'Vehicles & Remote Control'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  mamasandpapas: {
    name: 'Mamas & Papas',
    url: 'https://www.mamasandpapas.com/',
    type: 'baby',
    categories: [
      'Pushchairs & Travel Systems',
      'Car Seats',
      'Nursery Furniture',
      'Baby Clothing',
      'Feeding',
      'Baby Toys',
      'Maternity',
      'Gifts'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  selfridges: {
    name: 'Selfridges',
    url: 'https://www.selfridges.com/GB/en/',
    type: 'luxury',
    categories: [
      'Women',
      'Men',
      'Kids',
      'Beauty',
      'Home & Tech',
      'Food & Wine',
      'Gifts',
      'Sport'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  next: {
    name: 'Next',
    url: 'https://www.next.co.uk/',
    type: 'fashion',
    categories: [
      'Women',
      'Men',
      'Kids',
      'Baby',
      'Home',
      'Garden',
      'Gifts',
      'Beauty'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  primark: {
    name: 'Primark',
    url: 'https://www.primark.com/en-gb',
    type: 'fashion',
    categories: [
      'Women',
      'Men',
      'Kids',
      'Baby',
      'Home',
      'Beauty',
      'Accessories'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  },
  thetoyshop: {
    name: 'The Toy Shop',
    url: 'https://www.thetoyshop.com/',
    type: 'toys',
    categories: [
      'Action Figures',
      'Arts & Crafts',
      'Baby & Toddler',
      'Building Toys',
      'Dolls',
      'Educational Toys',
      'Games & Puzzles',
      'Outdoor Toys',
      'Vehicles'
    ],
    selectors: {
      // To be populated after site analysis
    },
    strategy: 'standard'
  }
};

export const SCRAPING_CONFIG = {
  timeout: 30000,
  waitForSelector: 5000,
  screenshotDelay: 2000,
  retryAttempts: 3,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

  // Product limits for testing
  maxProducts: 100, // Limit to 100 products for testing
  maxProductsPerCategory: 50, // Max products per category
  testMode: true // Enable test mode with limits
};
