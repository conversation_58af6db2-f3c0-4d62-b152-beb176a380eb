# Getting Started with Stagehand/Browserbase Scraper POC

## Quick Start

### 1. Prerequisites

- Node.js (v18 or higher)
- A Browserbase account and API key
- Git (optional, for version control)

### 2. Setup

```bash
# Clone or navigate to the project directory
cd scrapper

# Install dependencies (already done)
npm install

# Run setup script
npm run setup

# Edit .env file with your Browserbase API key
# BROWSERBASE_API_KEY=your_api_key_here
```

### 3. Get Your Browserbase API Key

1. Visit [Browserbase.com](https://browserbase.com)
2. Sign up for an account
3. Navigate to your dashboard
4. Copy your API key
5. Add it to your `.env` file

### 4. Verify Setup

```bash
# Run setup tests
npm test
```

You should see all tests pass with green checkmarks.

## Usage Examples

### Test a Single Site

```bash
# Test Waitrose
npm run test:site -- --site=waitrose

# Test Tesco
npm run test:site -- --site=tesco

# Test any configured site
npm run test:site -- --site=<sitename>
```

Available sites:
- `waitrose` - Waitrose grocery
- `tesco` - Tesco grocery
- `hamleys` - Hamleys toys
- `mamasandpapas` - Mama<PERSON> & Papas baby products
- `selfridges` - Selfridges luxury retail
- `next` - Next fashion
- `primark` - Primark fashion
- `thetoyshop` - The Toy Shop

### Run All Sites

```bash
# Analyze all configured sites
npm start

# Run in development mode (with file watching)
npm run dev
```

## Understanding the Output

### Console Output

The application provides detailed console logging:

- ℹ️ Info messages (navigation, progress)
- ✅ Success messages (completed operations)
- ⚠️ Warning messages (non-critical issues)
- ❌ Error messages (failures)
- 🐛 Debug messages (when DEBUG=true in .env)

### File Output

#### Screenshots (`screenshots/` directory)
- Full-page screenshots of each analyzed site
- Filename format: `{sitename}_{description}_{timestamp}.png`
- Example: `waitrose_initial_2025-08-01T14-00-00-000Z.png`

#### Analysis Results (`results/` directory)
- Detailed JSON reports for each site
- Filename format: `{sitename}_structure_{timestamp}.json`
- Contains:
  - Site metadata (title, URL, meta tags)
  - Page structure analysis
  - Forms and inputs inventory
  - Navigation links
  - Image catalog
  - Session information

### Sample Analysis Output

```json
{
  "site": {
    "name": "Waitrose",
    "url": "https://www.waitrose.com/",
    "type": "grocery"
  },
  "analysis": {
    "title": "Waitrose & Partners",
    "url": "https://www.waitrose.com/",
    "meta": {
      "description": "Shop online at Waitrose & Partners...",
      "keywords": "waitrose, groceries, online shopping"
    },
    "structure": {
      "hasHeader": true,
      "hasNav": true,
      "hasMain": true,
      "hasFooter": true,
      "hasSidebar": false
    },
    "forms": [...],
    "links": [...],
    "images": [...]
  },
  "timestamp": "2025-08-01T14:00:00.000Z",
  "sessionId": "session_123456"
}
```

## Troubleshooting

### Common Issues

1. **Missing API Key**
   ```
   Error: BROWSERBASE_API_KEY is required
   ```
   Solution: Add your API key to the `.env` file

2. **Network Timeouts**
   ```
   Error: Navigation timeout
   ```
   Solution: Check your internet connection and try again

3. **Site Access Issues**
   ```
   Error: Failed to navigate to site
   ```
   Solution: Some sites may have anti-bot protection; this is expected

### Debug Mode

Enable debug logging by adding to your `.env` file:
```
DEBUG=true
```

### Verbose Output

For detailed analysis of a specific site:
```bash
npm run test:site -- --site=waitrose
```

This provides more detailed console output than running all sites.

## Next Steps

1. **Analyze Site Structure**: Review the generated JSON files to understand each site's structure
2. **Customize Selectors**: Update `src/config/sites.js` with specific selectors for data extraction
3. **Extend Functionality**: Add custom scraping logic by extending the `BaseScraper` class
4. **Add New Sites**: Configure additional sites in the sites configuration
5. **Implement Data Extraction**: Build specific extractors for products, prices, or other data

## Support

- Check the `ARCHITECTURE.md` file for detailed technical information
- Review the console logs for specific error messages
- Ensure your Browserbase account has sufficient credits
- Verify that target sites are accessible from your network
