# Project Architecture

## Overview

This POC demonstrates web scraping automation using Stagehand/Browserbase for analyzing e-commerce and retail websites. The project is designed to be modular, extensible, and easy to understand.

## Project Structure

```
scrapper/
├── src/
│   ├── config/
│   │   ├── browserbase.js      # Browserbase configuration and validation
│   │   └── sites.js            # Target sites configuration
│   ├── scrapers/
│   │   └── BaseScraper.js      # Base scraper class with common functionality
│   ├── utils/
│   │   ├── logger.js           # Logging utility
│   │   └── fileUtils.js        # File operations (JSON, screenshots)
│   ├── index.js                # Main application entry point
│   ├── test.js                 # Setup validation tests
│   └── testSite.js             # Individual site testing
├── data/                       # Raw scraped data
├── screenshots/                # Page screenshots
├── results/                    # Analysis results (JSON)
├── .env                        # Environment variables
├── .env.example               # Environment template
├── package.json               # Dependencies and scripts
├── setup.js                   # Initial setup script
└── README.md                  # Project documentation
```

## Key Components

### 1. Configuration (`src/config/`)

- **browserbase.js**: Manages Browserbase API configuration, session settings, and validation
- **sites.js**: Defines target websites with metadata (URL, type, selectors, strategy)

### 2. Scrapers (`src/scrapers/`)

- **BaseScraper.js**: Core scraping functionality including:
  - Session initialization and management
  - Navigation and page interaction
  - Structure analysis and data extraction
  - Screenshot capture
  - Error handling and cleanup

### 3. Utilities (`src/utils/`)

- **logger.js**: Structured logging with timestamps and different log levels
- **fileUtils.js**: File operations for saving JSON data, screenshots, and results

### 4. Main Scripts

- **index.js**: Main application that can run all sites or specific sites
- **test.js**: Validation script to verify setup and configuration
- **testSite.js**: Detailed testing for individual sites
- **setup.js**: Initial project setup and environment configuration

## Data Flow

1. **Initialization**: Validate configuration and create Browserbase session
2. **Navigation**: Navigate to target website with proper timeouts
3. **Analysis**: Extract page structure, forms, links, and metadata
4. **Capture**: Take screenshots for visual verification
5. **Storage**: Save analysis results and screenshots to disk
6. **Cleanup**: Properly close sessions and free resources

## Target Websites

The POC is configured to analyze these 8 websites:

1. **Waitrose** - Grocery (https://www.waitrose.com/)
2. **Tesco** - Grocery (https://www.tesco.com/groceries/en-GB)
3. **Hamleys** - Toys (https://www.hamleys.com/)
4. **Mamas & Papas** - Baby products (https://www.mamasandpapas.com/)
5. **Selfridges** - Luxury retail (https://www.selfridges.com/GB/en/)
6. **Next** - Fashion (https://www.next.co.uk/)
7. **Primark** - Fashion (https://www.primark.com/en-gb)
8. **The Toy Shop** - Toys (https://www.thetoyshop.com/)

## Analysis Features

Each site analysis includes:

- **Page metadata** (title, description, keywords)
- **Structural elements** (header, nav, main, footer, sidebar)
- **Forms analysis** (inputs, methods, actions)
- **Navigation links** (internal/external classification)
- **Image inventory** (src, alt text, dimensions)
- **Screenshots** (full page captures)

## Extensibility

The architecture supports easy extension:

- **New sites**: Add to `SITES` configuration in `sites.js`
- **Custom scrapers**: Extend `BaseScraper` for site-specific logic
- **Additional analysis**: Extend `analyzeStructure()` method
- **Different strategies**: Implement custom scraping strategies per site

## Error Handling

- Comprehensive try-catch blocks with detailed logging
- Graceful session cleanup on failures
- Retry logic for network issues
- Validation of configuration before execution

## Output

- **Screenshots**: `screenshots/` directory with timestamped PNG files
- **Analysis data**: `results/` directory with detailed JSON reports
- **Logs**: Console output with structured logging and timestamps
