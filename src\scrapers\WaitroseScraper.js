import { ProductScraper } from './ProductScraper.js';
import { Logger } from '../utils/logger.js';

/**
 * Waitrose-specific product scraper
 * Navigation: Homepage → GROCERIES → Category → Subcategory → Products
 * Pagination: "Load more" button
 */
export class WaitroseScraper extends ProductScraper {
  constructor(siteConfig) {
    super(siteConfig);
    this.categories = [
      'Fresh & Chilled',
      'Frozen',
      'Bakery',
      'Food Cupboard',
      'Beer, Wine & Spirits',
      'Tea, Coffee & Soft Drinks'
    ];
  }

  /**
   * Handle Waitrose cookie consent
   */
  async handleCookies() {
    try {
      Logger.info('Handling Waitrose cookie consent...');
      
      // Wait for cookie dialog to appear
      await this.stagehand.page.waitForTimeout(2000);
      
      // Look for and click "Allow all" button
      await this.stagehand.page.act('Click the "Allow all" button for cookies');
      
      // Wait for dialog to close
      await this.stagehand.page.waitForTimeout(1000);
      
      Logger.success('Cookie consent handled successfully');
    } catch (error) {
      Logger.warning('Cookie consent handling failed or not needed', error.message);
      // Continue anyway as cookies might not be required
    }
  }

  /**
   * Navigate to Waitrose category
   */
  async navigateToCategory(category = 'Fresh & Chilled') {
    try {
      Logger.info(`Navigating to Waitrose category: ${category}`);
      
      // First click on GROCERIES main navigation
      await this.stagehand.page.act('Click on "GROCERIES" navigation link');
      await this.stagehand.page.waitForTimeout(2000);
      
      // Then click on the specific category
      await this.stagehand.page.act(`Click on "${category}" category link`);
      await this.stagehand.page.waitForTimeout(3000);
      
      Logger.success(`Successfully navigated to ${category}`);
    } catch (error) {
      Logger.error(`Failed to navigate to category: ${category}`, error);
      throw error;
    }
  }

  /**
   * Extract products from current Waitrose page
   */
  async extractProducts() {
    try {
      Logger.info('Extracting products from Waitrose page...');
      
      const products = await this.stagehand.page.extract(`
        Extract all product information from the current page. For each product, get:
        - Product name
        - Price (including currency)
        - Price per unit (if available)
        - Product description or details
        - Product image URL
        - Product page link
        - Any special offers or badges
        - Availability status
        
        Return as an array of product objects with these fields:
        {
          "name": "product name",
          "price": "£X.XX",
          "pricePerUnit": "£X.XX per unit",
          "description": "product description",
          "imageUrl": "image URL",
          "productUrl": "product page URL",
          "offers": ["offer text"],
          "available": true/false,
          "category": "current category"
        }
      `);
      
      if (products && Array.isArray(products)) {
        Logger.success(`Extracted ${products.length} products from Waitrose`);
        return products;
      } else {
        Logger.warning('No products extracted or invalid format');
        return [];
      }
    } catch (error) {
      Logger.error('Failed to extract products from Waitrose', error);
      return [];
    }
  }

  /**
   * Handle Waitrose pagination (Load more button)
   */
  async handlePagination() {
    try {
      Logger.info('Checking for Waitrose "Load more" button...');
      
      // Try to find and click the "Load more" button
      await this.stagehand.page.act('Click the "Load more" button');
      
      Logger.success('Successfully clicked "Load more" button');
      return true; // More content available
      
    } catch (error) {
      Logger.info('No more content to load or "Load more" button not found');
      return false; // No more content
    }
  }

  /**
   * Get available categories for Waitrose
   */
  getAvailableCategories() {
    return this.categories;
  }

  /**
   * Scrape specific Waitrose categories
   */
  async scrapeWaitrose(selectedCategories = null) {
    const categoriesToScrape = selectedCategories || this.categories.slice(0, 2); // Default to first 2 categories
    
    Logger.info(`Starting Waitrose scraping for categories: ${categoriesToScrape.join(', ')}`);
    
    return await this.scrapeProducts(categoriesToScrape);
  }

  /**
   * Quick scrape for testing (single category)
   */
  async quickScrape() {
    Logger.info('Starting Waitrose quick scrape (Fresh & Chilled only)');
    return await this.scrapeProducts(['Fresh & Chilled']);
  }
}
