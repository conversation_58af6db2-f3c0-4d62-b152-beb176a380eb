<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <button onclick="testFunction()">Test Button</button>
    <button onclick="dashboard.startScraping('waitrose')">Test Dashboard Function</button>
    
    <div id="output"></div>
    
    <script>
        function testFunction() {
            console.log('Test function called');
            document.getElementById('output').innerHTML += '<p>Test function called</p>';
        }
        
        // Load the dashboard script
        const script = document.createElement('script');
        script.src = '/app.js';
        script.onload = function() {
            console.log('Dashboard script loaded');
            document.getElementById('output').innerHTML += '<p>Dashboard script loaded</p>';
            
            // Test if dashboard object exists
            if (typeof dashboard !== 'undefined') {
                console.log('Dashboard object exists:', dashboard);
                document.getElementById('output').innerHTML += '<p>Dashboard object exists</p>';
            } else {
                console.log('Dashboard object not found');
                document.getElementById('output').innerHTML += '<p>Dashboard object NOT found</p>';
            }
        };
        script.onerror = function() {
            console.log('Failed to load dashboard script');
            document.getElementById('output').innerHTML += '<p>Failed to load dashboard script</p>';
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
