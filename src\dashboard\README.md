# Web Scraper Dashboard

A simple, modern web dashboard for managing and monitoring web scraping operations.

## Features

### 🌐 Site Management
- View all configured scraping targets
- Start scraping jobs with one click
- See site details including URL and type

### 📊 Job Monitoring
- Real-time job status updates via WebSocket
- Progress tracking for running jobs
- Detailed job logs and error reporting
- Cancel running jobs

### 📁 Results Management
- Browse and download scraping results
- File size and modification date display
- Direct download links for result files

### 🎨 Modern UI
- Responsive design that works on all devices
- Real-time updates without page refresh
- Clean, professional interface
- Dark mode support

## Getting Started

### Prerequisites
- Node.js 18+
- Browserbase API credentials configured
- Web scraper project dependencies installed

### Installation

1. Install dashboard dependencies (already included in main package.json):
```bash
npm install
```

2. Start the dashboard server:
```bash
npm run dashboard
```

3. Open your browser and navigate to:
```
http://localhost:3000
```

### Development Mode

For development with auto-restart on file changes:
```bash
npm run dashboard:dev
```

## API Endpoints

The dashboard provides a REST API for programmatic access:

### Sites
- `GET /api/sites` - List all configured sites
- `POST /api/scrape/:site` - Start scraping a specific site

### Jobs
- `GET /api/jobs` - List all jobs
- `GET /api/jobs/:jobId` - Get specific job details
- `DELETE /api/jobs/:jobId` - Cancel a running job

### Results
- `GET /api/results` - List all result files
- `GET /api/results/:filename` - Download a specific result file

## WebSocket Events

Real-time updates are provided via WebSocket:

- `jobs_update` - Sent when job status changes
- Connection automatically reconnects on disconnect

## Configuration

The dashboard uses the same configuration as the main scraper:

- **Sites**: Configured in `src/config/sites.js`
- **Browserbase**: Configured via environment variables
- **Port**: Set via `PORT` environment variable (default: 3000)

## File Structure

```
src/dashboard/
├── server.js          # Express server and API
├── public/
│   ├── index.html     # Main dashboard page
│   ├── styles.css     # Dashboard styles
│   └── app.js         # Frontend JavaScript
└── README.md          # This file
```

## Usage

1. **Start Scraping**: Click "Start Scraping" on any site card
2. **Monitor Progress**: Watch real-time updates in the Active Jobs section
3. **View Details**: Click "Details" on any job to see logs and results
4. **Download Results**: Use the Results section to download scraped data
5. **Cancel Jobs**: Click "Cancel" to stop running jobs

## Troubleshooting

### Dashboard won't start
- Check that port 3000 is available
- Verify Browserbase credentials are configured
- Ensure all dependencies are installed

### Jobs not updating
- Check WebSocket connection in browser dev tools
- Verify firewall isn't blocking WebSocket connections
- Refresh the page to reconnect

### No results showing
- Complete at least one scraping job
- Check the `results/` directory exists
- Verify file permissions

## Security Notes

- Dashboard is intended for local development use
- No authentication is implemented
- Don't expose to public internet without proper security measures

## Contributing

The dashboard is part of the larger web scraper project. See the main project README for contribution guidelines.
