import { WaitroseScraper } from './src/scrapers/WaitroseScraper.js';
import { SITES } from './src/config/sites.js';

async function testUndefinedCategories() {
  console.log('🧪 Testing Waitrose scraper with undefined categories...\n');

  const siteConfig = SITES.waitrose;
  const scraper = new WaitroseScraper(siteConfig);
  
  try {
    console.log('📋 Calling scrapeProducts(undefined)...');
    const result = await scraper.scrapeProducts(undefined);
    
    console.log('\n✅ Result:', {
      success: result.success,
      totalProducts: result.totalProducts,
      productsLength: result.products?.length,
      resultsPath: result.resultsPath
    });
    
    if (result.products && result.products.length > 0) {
      console.log('\n📦 First few products:');
      result.products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ${product.price}`);
      });
    } else {
      console.log('\n❌ No products found!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    try {
      await scraper.close();
      console.log('\n🔒 Scraper closed successfully');
    } catch (error) {
      console.error('⚠️ Error closing scraper:', error.message);
    }
  }
}

testUndefinedCategories();
