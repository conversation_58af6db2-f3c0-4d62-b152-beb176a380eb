#!/usr/bin/env node

import { validateConfig } from './config/browserbase.js';
import { SITES } from './config/sites.js';
import { Logger } from './utils/logger.js';

/**
 * Simple test to verify setup
 */
async function runTests() {
  try {
    Logger.info('🧪 Running setup tests...\n');

    // Test 1: Configuration validation
    Logger.info('Test 1: Configuration validation');
    try {
      validateConfig();
      Logger.success('✅ Configuration is valid');
    } catch (error) {
      Logger.error('❌ Configuration validation failed');
      Logger.error('Make sure to:');
      Logger.error('1. Copy .env.example to .env');
      Logger.error('2. Add your BROWSERBASE_API_KEY to .env');
      throw error;
    }

    // Test 2: Sites configuration
    Logger.info('\nTest 2: Sites configuration');
    const siteCount = Object.keys(SITES).length;
    Logger.success(`✅ ${siteCount} sites configured:`);
    Object.entries(SITES).forEach(([key, site]) => {
      Logger.info(`   - ${key}: ${site.name} (${site.url})`);
    });

    // Test 3: Import test
    Logger.info('\nTest 3: Package imports');
    try {
      const { Stagehand } = await import('@browserbasehq/stagehand');
      Logger.success('✅ Stagehand package imported successfully');
    } catch (error) {
      Logger.error('❌ Failed to import Stagehand package', error);
      throw error;
    }

    // Test 4: Directory structure
    Logger.info('\nTest 4: Directory structure');
    const fs = await import('fs/promises');
    const requiredDirs = ['src', 'src/config', 'src/scrapers', 'src/utils', 'data', 'screenshots', 'results'];
    
    for (const dir of requiredDirs) {
      try {
        await fs.access(dir);
        Logger.success(`✅ Directory exists: ${dir}`);
      } catch {
        Logger.error(`❌ Directory missing: ${dir}`);
      }
    }

    Logger.info('\n🎉 All tests passed! Setup is complete.');
    Logger.info('\nNext steps:');
    Logger.info('1. Make sure your .env file has a valid BROWSERBASE_API_KEY');
    Logger.info('2. Run a single site test: npm run test:site -- --site=waitrose');
    Logger.info('3. Run all sites: npm start');

  } catch (error) {
    Logger.error('\n❌ Setup tests failed', error);
    process.exit(1);
  }
}

// Run tests
runTests();
