class ScraperDashboard {
    constructor() {
        this.ws = null;
        this.sites = [];
        this.jobs = [];
        this.results = [];
        this.currentSite = null; // For category selection

        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.connectWebSocket();
        await this.loadSites();
        await this.loadResults();
        this.startPeriodicRefresh();
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshAll();
        });

        // Modal close
        document.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal on outside click
        document.getElementById('jobModal').addEventListener('click', (e) => {
            if (e.target.id === 'jobModal') {
                this.closeModal();
            }
        });

        // Category modal event listeners
        document.getElementById('categoryModal').addEventListener('click', (e) => {
            if (e.target.id === 'categoryModal') {
                this.closeCategoryModal();
            }
        });

        // Category selection buttons
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            this.selectAllCategories();
        });

        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllCategories();
        });

        document.getElementById('startScrapingBtn').addEventListener('click', () => {
            this.startScrapingWithSelectedCategories();
        });
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected, attempting to reconnect...');
            setTimeout(() => this.connectWebSocket(), 3000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'jobs_update':
                this.jobs = message.data;
                this.renderJobs();
                break;
        }
    }

    async loadSites() {
        try {
            const response = await fetch('/api/sites');
            const data = await response.json();
            this.sites = data.sites;
            this.renderSites();
        } catch (error) {
            console.error('Failed to load sites:', error);
            this.showError('Failed to load sites');
        }
    }

    async loadResults() {
        try {
            const response = await fetch('/api/results');
            const data = await response.json();
            this.results = data.files;
            this.renderResults();
        } catch (error) {
            console.error('Failed to load results:', error);
        }
    }

    async startScraping(siteId) {
        console.log('startScraping called with siteId:', siteId);

        // Find the site to get its categories
        const site = this.sites.find(s => s.id === siteId);
        console.log('Found site:', site);

        if (!site) {
            console.error('Site not found for siteId:', siteId);
            this.showError('Site not found');
            return;
        }

        // If site has categories, show category selection modal
        if (site.categories && site.categories.length > 0) {
            console.log('Site has categories, showing modal');
            this.showCategoryModal(site);
        } else {
            console.log('Site has no categories, starting scraping directly');
            // No categories available, start scraping with default
            this.startScrapingWithCategories(siteId, null);
        }
    }

    async startScrapingWithCategories(siteId, categories) {
        console.log('startScrapingWithCategories called with:', { siteId, categories });

        try {
            const requestBody = {};
            if (categories && categories.length > 0) {
                requestBody.categories = categories;
            }

            console.log('Making API request to:', `/api/scrape/${siteId}`, 'with body:', requestBody);

            const response = await fetch(`/api/scrape/${siteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('API response status:', response.status);

            if (!response.ok) {
                const error = await response.json();
                console.error('API error response:', error);
                throw new Error(error.error || 'Failed to start scraping');
            }

            const data = await response.json();
            console.log('API success response:', data);
            this.showSuccess(`Started scraping: ${data.message}`);
        } catch (error) {
            console.error('Failed to start scraping:', error);
            this.showError(error.message);
        }
    }

    async cancelJob(jobId) {
        try {
            const response = await fetch(`/api/jobs/${jobId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error('Failed to cancel job');
            }
            
            this.showSuccess('Job cancelled');
        } catch (error) {
            console.error('Failed to cancel job:', error);
            this.showError('Failed to cancel job');
        }
    }

    renderSites() {
        const container = document.getElementById('sitesGrid');
        
        if (this.sites.length === 0) {
            container.innerHTML = '<p>No sites configured</p>';
            return;
        }
        
        container.innerHTML = this.sites.map(site => `
            <div class="site-card">
                <div class="site-header">
                    <div class="site-info">
                        <h3>${site.name}</h3>
                        <span class="site-type">${site.type}</span>
                    </div>
                </div>
                <div class="site-url">${site.url}</div>
                ${site.categories && site.categories.length > 0 ? `
                    <div class="site-categories">
                        <small><i class="fas fa-tags"></i> ${site.categories.length} categories available</small>
                    </div>
                ` : ''}
                <button class="btn btn-primary" onclick="console.log('Button clicked for site:', '${site.id}'); dashboard.startScraping('${site.id}')">
                    <i class="fas fa-play"></i> Start Scraping
                </button>
            </div>
        `).join('');
    }

    renderJobs() {
        const container = document.getElementById('jobsContainer');
        
        if (this.jobs.length === 0) {
            container.innerHTML = `
                <div class="no-jobs">
                    <i class="fas fa-clipboard-list"></i>
                    <p>No active jobs. Start scraping a site to see jobs here.</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.jobs.map(job => `
            <div class="job-card">
                <div class="job-header">
                    <div class="job-title">${job.siteName}</div>
                    <span class="job-status status-${job.status}">${job.status}</span>
                </div>
                
                ${job.status === 'running' ? `
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${job.progress || 0}%"></div>
                    </div>
                ` : ''}
                
                <div class="job-meta">
                    <small>Started: ${new Date(job.startTime).toLocaleString()}</small>
                    ${job.endTime ? `<br><small>Ended: ${new Date(job.endTime).toLocaleString()}</small>` : ''}
                </div>
                
                <div class="job-actions">
                    <button class="btn btn-secondary" onclick="dashboard.showJobDetails('${job.id}')">
                        <i class="fas fa-info-circle"></i> Details
                    </button>
                    ${job.status === 'running' ? `
                        <button class="btn btn-danger" onclick="dashboard.cancelJob('${job.id}')">
                            <i class="fas fa-stop"></i> Cancel
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    renderResults() {
        const container = document.getElementById('resultsContainer');
        
        if (this.results.length === 0) {
            container.innerHTML = '<p>No results available yet. Complete some scraping jobs to see results here.</p>';
            return;
        }
        
        container.innerHTML = `
            <div class="results-grid">
                ${this.results.map(file => `
                    <div class="result-card">
                        <div class="result-name">${file.name}</div>
                        <div class="result-meta">
                            Size: ${this.formatFileSize(file.size)}<br>
                            Modified: ${new Date(file.modified).toLocaleString()}
                        </div>
                        <a href="/api/results/${file.name}" class="btn btn-primary" download>
                            <i class="fas fa-download"></i> Download
                        </a>
                    </div>
                `).join('')}
            </div>
        `;
    }

    showJobDetails(jobId) {
        const job = this.jobs.find(j => j.id === jobId);
        if (!job) return;
        
        const modal = document.getElementById('jobModal');
        const title = document.getElementById('modalTitle');
        const content = document.getElementById('modalContent');
        
        title.textContent = `Job Details - ${job.siteName}`;
        
        content.innerHTML = `
            <div class="job-details">
                <h4>Job Information</h4>
                <p><strong>ID:</strong> ${job.id}</p>
                <p><strong>Site:</strong> ${job.siteName}</p>
                <p><strong>Status:</strong> <span class="job-status status-${job.status}">${job.status}</span></p>
                <p><strong>Started:</strong> ${new Date(job.startTime).toLocaleString()}</p>
                ${job.endTime ? `<p><strong>Ended:</strong> ${new Date(job.endTime).toLocaleString()}</p>` : ''}
                ${job.error ? `<p><strong>Error:</strong> <span style="color: #e53e3e;">${job.error}</span></p>` : ''}
                
                <h4 style="margin-top: 20px;">Logs</h4>
                <div class="job-logs">
                    ${job.logs.map(log => `<div class="log-entry">${log}</div>`).join('')}
                </div>
                
                ${job.result ? `
                    <h4 style="margin-top: 20px;">Result Summary</h4>
                    <pre style="background: #f7fafc; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 0.9rem;">
${JSON.stringify(job.result, null, 2)}
                    </pre>
                ` : ''}
            </div>
        `;
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('jobModal').style.display = 'none';
    }

    async refreshAll() {
        await Promise.all([
            this.loadSites(),
            this.loadResults()
        ]);
        this.showSuccess('Dashboard refreshed');
    }

    startPeriodicRefresh() {
        // Refresh results every 30 seconds
        setInterval(() => {
            this.loadResults();
        }, 30000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            background: ${type === 'success' ? '#48bb78' : '#e53e3e'};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Category Modal Methods
    showCategoryModal(site) {
        this.currentSite = site;
        const modal = document.getElementById('categoryModal');
        const title = document.getElementById('categoryModalTitle');
        const categoryList = document.getElementById('categoryList');

        title.textContent = `Select Categories for ${site.name}`;

        // Render categories
        categoryList.innerHTML = site.categories.map(category => `
            <div class="category-item">
                <input type="checkbox" id="cat_${category.replace(/\s+/g, '_')}" value="${category}">
                <label for="cat_${category.replace(/\s+/g, '_')}">${category}</label>
            </div>
        `).join('');

        modal.style.display = 'block';
    }

    closeCategoryModal() {
        document.getElementById('categoryModal').style.display = 'none';
        this.currentSite = null;
    }

    selectAllCategories() {
        const checkboxes = document.querySelectorAll('#categoryList input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    clearAllCategories() {
        const checkboxes = document.querySelectorAll('#categoryList input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    startScrapingWithSelectedCategories() {
        if (!this.currentSite) {
            this.showError('No site selected');
            return;
        }

        const checkboxes = document.querySelectorAll('#categoryList input[type="checkbox"]:checked');
        const selectedCategories = Array.from(checkboxes).map(cb => cb.value);

        if (selectedCategories.length === 0) {
            this.showError('Please select at least one category');
            return;
        }

        // Get the site ID before closing the modal (which sets currentSite to null)
        const siteId = this.currentSite.id;
        this.closeCategoryModal();
        this.startScrapingWithCategories(siteId, selectedCategories);
    }
}

// Global test function
window.testClick = function() {
    console.log('Global test function called!');
    alert('Global test function called!');
};

// Initialize dashboard
const dashboard = new ScraperDashboard();

// Make dashboard globally available for debugging
window.dashboard = dashboard;
