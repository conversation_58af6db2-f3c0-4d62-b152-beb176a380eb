# Stagehand/Browserbase Scraper POC

A proof of concept for testing Stagehand/Browserbase automation for web scraping various e-commerce and retail websites.

## Target Websites

- [Waitrose](https://www.waitrose.com/)
- [Tesco](https://www.tesco.com/groceries/en-GB) --> ANTI-BOT PROTECTION
- [<PERSON><PERSON><PERSON>](https://www.hamleys.com/) --> HAMLEYS IS BLOCKING US
- [Mama<PERSON> & Papas](https://www.mamasandpapas.com/) --> ANTI-BOT PROTECTION
- [Selfridges](https://www.selfridges.com/GB/en/) --> ANTI-BOT PROTECTION
- [Next](https://www.next.co.uk/)
- [Primark](https://www.primark.com/en-gb)
- [The Toy Shop](https://www.thetoyshop.com/)

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Copy environment file and configure:
   ```bash
   cp .env.example .env
   ```

3. Add your Browserbase API key to `.env`:
   ```
   BROWSERBASE_API_KEY=your_api_key_here
   ```

## Usage

### Run all site tests
```bash
npm start
```

### Run in development mode (with file watching)
```bash
npm run dev
```

### Test a specific site
```bash
npm run test:site -- --site=waitrose
```

## Project Structure

```
src/
├── index.js          # Main entry point
├── config/           # Configuration files
├── scrapers/         # Individual site scrapers
├── utils/            # Utility functions
└── test.js          # Test runner
```

## Features

- Automated browser sessions using Browserbase
- Site structure analysis
- Screenshot capture
- Data extraction
- Error handling and retry logic
- Configurable scraping strategies per site
