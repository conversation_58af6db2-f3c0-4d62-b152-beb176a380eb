import { ProductScraper } from './ProductScraper.js';
import { Logger } from '../utils/logger.js';

/**
 * Next-specific product scraper
 * Navigation: Homepage → Category (WOMEN/MEN/etc.) → Subcategory → Products
 * Pagination: Infinite scroll/dynamic loading
 */
export class NextScraper extends ProductScraper {
  constructor(siteConfig) {
    super(siteConfig);
    this.categories = [
      { main: 'WOMEN', sub: 'Dresses' },
      { main: 'WOMEN', sub: 'Tops & T-Shirts' },
      { main: 'MEN', sub: 'T-Shirts & Polos' },
      { main: 'MEN', sub: 'Jeans' },
      { main: 'BOYS', sub: 'T-Shirts & Tops' },
      { main: 'GIRLS', sub: 'Dresses' }
    ];
  }

  /**
   * Handle Next cookie consent
   */
  async handleCookies() {
    try {
      Logger.info('Handling Next cookie consent...');

      // Use Stagehand for AI-powered clicking with built-in waiting
      await this.stagehand.page.act('Click the "ACCEPT ALL COOKIES" button', {
        domSettleTimeoutMs: 3000
      });

      Logger.success('Cookie consent handled successfully');
    } catch (error) {
      Logger.warning('Cookie consent handling failed or not needed', error.message);
      // Continue anyway as cookies might not be required
    }
  }

  /**
   * Navigate to Next category
   */
  async navigateToCategory(categoryObj = { main: 'WOMEN', sub: 'Dresses' }) {
    try {
      const { main, sub } = typeof categoryObj === 'string' 
        ? { main: 'WOMEN', sub: categoryObj }
        : categoryObj;
        
      Logger.info(`Navigating to Next category: ${main} > ${sub}`);
      
      // Click on main category (e.g., WOMEN)
      await this.stagehand.page.act(`Click on "${main}" navigation link`);
      await this.stagehand.page.waitForTimeout(2000);
      
      // Click on subcategory (e.g., Dresses)
      await this.stagehand.page.act(`Click on "${sub}" category link`);
      await this.stagehand.page.waitForTimeout(3000);
      
      Logger.success(`Successfully navigated to ${main} > ${sub}`);
    } catch (error) {
      Logger.error(`Failed to navigate to category: ${JSON.stringify(categoryObj)}`, error);
      throw error;
    }
  }

  /**
   * Extract products from current Next page
   */
  async extractProducts() {
    try {
      Logger.info('Extracting products from Next page...');
      
      const products = await this.stagehand.page.extract(`
        Extract all product information from the current page. For each product, get:
        - Product name
        - Price (current price and any original price if on sale)
        - Product description or style details
        - Product image URL
        - Product page link
        - Available colors/variants
        - Size information if visible
        - Sale/discount information
        - Brand information
        
        Return as an array of product objects with these fields:
        {
          "name": "product name",
          "price": "£X.XX",
          "originalPrice": "£X.XX" (if on sale),
          "description": "product description",
          "imageUrl": "image URL",
          "productUrl": "product page URL",
          "colors": ["color1", "color2"],
          "sizes": ["S", "M", "L"],
          "onSale": true/false,
          "brand": "brand name",
          "category": "current category"
        }
      `);
      
      if (products && Array.isArray(products)) {
        Logger.success(`Extracted ${products.length} products from Next`);
        return products;
      } else {
        Logger.warning('No products extracted or invalid format');
        return [];
      }
    } catch (error) {
      Logger.error('Failed to extract products from Next', error);
      return [];
    }
  }

  /**
   * Handle Next pagination (Infinite scroll)
   */
  async handlePagination() {
    try {
      Logger.info('Attempting to load more products via scroll...');
      
      // Get current page height
      const previousHeight = await this.stagehand.page.evaluate(() => document.body.scrollHeight);
      
      // Scroll down to trigger loading with DOM settling
      await this.stagehand.page.act('Scroll down to load more products', {
        domSettleTimeoutMs: 3000
      });
      
      // Check if page height increased (new content loaded)
      const currentHeight = await this.stagehand.page.evaluate(() => document.body.scrollHeight);
      
      if (currentHeight > previousHeight) {
        Logger.success('New content loaded via scroll');
        return true; // More content available
      } else {
        Logger.info('No new content loaded - reached end');
        return false; // No more content
      }
      
    } catch (error) {
      Logger.info('Pagination ended or failed', error.message);
      return false; // No more content
    }
  }

  /**
   * Get available categories for Next
   */
  getAvailableCategories() {
    return this.categories;
  }

  /**
   * Scrape specific Next categories
   */
  async scrapeNext(selectedCategories = null) {
    const categoriesToScrape = selectedCategories || this.categories.slice(0, 2); // Default to first 2 categories
    
    Logger.info(`Starting Next scraping for categories: ${categoriesToScrape.map(c => `${c.main}>${c.sub}`).join(', ')}`);
    
    return await this.scrapeProducts(categoriesToScrape);
  }

  /**
   * Quick scrape for testing (single category)
   */
  async quickScrape() {
    Logger.info('Starting Next quick scrape (Women > Dresses only)');
    return await this.scrapeProducts([{ main: 'WOMEN', sub: 'Dresses' }]);
  }
}
