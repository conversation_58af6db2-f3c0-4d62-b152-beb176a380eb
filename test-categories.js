#!/usr/bin/env node

import { SITES } from './src/config/sites.js';
import { ScraperManager } from './src/scrapers/ScraperManager.js';

console.log('Testing category functionality...\n');

// Test 1: Check if sites have categories in configuration
console.log('=== Test 1: Site Categories in Configuration ===');
Object.entries(SITES).forEach(([siteId, config]) => {
  console.log(`${config.name} (${siteId}):`);
  if (config.categories && Array.isArray(config.categories)) {
    console.log(`  ✅ ${config.categories.length} categories: ${config.categories.slice(0, 3).join(', ')}${config.categories.length > 3 ? '...' : ''}`);
  } else {
    console.log(`  ❌ No categories defined`);
  }
});

console.log('\n=== Test 2: ScraperManager Category Retrieval ===');
const scraperManager = new ScraperManager();

Object.keys(SITES).forEach(siteId => {
  const categories = scraperManager.getAvailableCategories(siteId);
  const isSupported = scraperManager.isSiteSupported(siteId);
  
  console.log(`${SITES[siteId].name} (${siteId}):`);
  console.log(`  Supported: ${isSupported ? '✅' : '❌'}`);
  console.log(`  Categories: ${categories.length > 0 ? `✅ ${categories.length} found` : '❌ None'}`);
  if (categories.length > 0) {
    console.log(`  Sample: ${categories.slice(0, 3).join(', ')}${categories.length > 3 ? '...' : ''}`);
  }
});

console.log('\n=== Test 3: API Response Format ===');
const sites = Object.entries(SITES).map(([key, config]) => ({
  id: key,
  name: config.name,
  url: config.url,
  type: config.type,
  strategy: config.strategy,
  supported: scraperManager.isSiteSupported(key),
  categories: scraperManager.isSiteSupported(key) ? scraperManager.getAvailableCategories(key) : []
}));

console.log('Sample API response for first 3 sites:');
sites.slice(0, 3).forEach(site => {
  console.log(`${site.name}:`);
  console.log(`  ID: ${site.id}`);
  console.log(`  Type: ${site.type}`);
  console.log(`  Supported: ${site.supported}`);
  console.log(`  Categories: ${site.categories.length} (${site.categories.slice(0, 2).join(', ')}${site.categories.length > 2 ? '...' : ''})`);
});

console.log('\n✅ Category functionality test completed!');
