import { ProductScraper } from './ProductScraper.js';
import { Logger } from '../utils/logger.js';
import { expect } from '@playwright/test';

/**
 * Primark-specific product scraper
 * Navigation: Homepage → Category → Subcategory → Product Collections
 * Pagination: "LOAD MORE" button with progress tracking
 */
export class PrimarkScraper extends ProductScraper {
  constructor(siteConfig) {
    super(siteConfig);
    this.categories = [
      { main: 'WOMEN', sub: 'TOPS AND T-SHIRTS' },
      { main: 'WOMEN', sub: 'JEANS' },
      { main: 'WOMEN', sub: 'SWEATERS AND CARDIGANS' },
      { main: 'MEN', sub: 'T-SHIRTS' },
      { main: 'MEN', sub: 'JEANS' },
      { main: 'KIDS', sub: 'TOPS' }
    ];
  }

  /**
   * Handle Primark cookie consent
   */
  async handleCookies() {
    try {
      Logger.info('Handling Primark cookie consent...');

      // Use Stagehand for AI-powered clicking with built-in waiting
      await this.stagehand.page.act('Click the "ACCEPT ALL COOKIES" button', {
        domSettleTimeoutMs: 3000
      });

      // Use web assertion to confirm cookie dialog is hidden
      const cookieDialog = this.stagehand.page.locator('[data-testid*="cookie"], [class*="cookie"], [id*="cookie"], [aria-label*="cookie"]');
      await expect(cookieDialog).toBeHidden({ timeout: 5000 });

      Logger.success('Cookie consent handled successfully');
    } catch (error) {
      Logger.warning('Cookie consent handling failed or not needed', error.message);
      // Continue anyway as cookies might not be required
    }
  }

  /**
   * Handle store finder banner if present
   */
  async handleStoreBanner() {
    try {
      Logger.info('Checking for Primark store finder banner...');
      
      // Try to close store banner if present
      await this.stagehand.page.act('Click the close button on the store finder banner');
      await this.stagehand.page.waitForTimeout(1000);
      
      Logger.success('Store banner closed');
    } catch (error) {
      Logger.info('No store banner found or already closed');
    }
  }

  /**
   * Navigate to Primark category
   */
  async navigateToCategory(categoryObj = { main: 'WOMEN', sub: 'TOPS AND T-SHIRTS' }) {
    try {
      const { main, sub } = typeof categoryObj === 'string' 
        ? { main: 'WOMEN', sub: categoryObj }
        : categoryObj;
        
      Logger.info(`Navigating to Primark category: ${main} > ${sub}`);
      
      // Handle store banner first
      await this.handleStoreBanner();
      
      // Click on main category (e.g., WOMEN)
      await this.stagehand.page.act(`Click on "${main}" navigation link`);
      await this.stagehand.page.waitForTimeout(2000);
      
      // Click on subcategory (e.g., TOPS AND T-SHIRTS)
      await this.stagehand.page.act(`Click on "${sub}" category link`);
      await this.stagehand.page.waitForTimeout(3000);
      
      Logger.success(`Successfully navigated to ${main} > ${sub}`);
    } catch (error) {
      Logger.error(`Failed to navigate to category: ${JSON.stringify(categoryObj)}`, error);
      throw error;
    }
  }

  /**
   * Extract products from current Primark page
   */
  async extractProducts() {
    try {
      Logger.info('Extracting products from Primark page...');
      
      const products = await this.stagehand.page.extract(`
        Extract all product information from the current page. For each product, get:
        - Product name
        - Price (in GBP)
        - Product description or style details
        - Product image URL
        - Product page link (if available)
        - Available colors/variants
        - Size information if visible
        - Any special collections or tags
        
        Return as an array of product objects with these fields:
        {
          "name": "product name",
          "price": "£X.XX",
          "description": "product description",
          "imageUrl": "image URL",
          "productUrl": "product page URL",
          "colors": ["color1", "color2"],
          "sizes": ["XS", "S", "M", "L", "XL"],
          "collection": "collection name",
          "category": "current category"
        }
      `);
      
      if (products && Array.isArray(products)) {
        Logger.success(`Extracted ${products.length} products from Primark`);
        return products;
      } else {
        Logger.warning('No products extracted or invalid format');
        return [];
      }
    } catch (error) {
      Logger.error('Failed to extract products from Primark', error);
      return [];
    }
  }

  /**
   * Handle Primark pagination (LOAD MORE button with progress)
   */
  async handlePagination() {
    try {
      Logger.info('Checking for Primark "LOAD MORE" button...');
      
      // Try to extract progress information first
      try {
        const progressInfo = await this.stagehand.page.extract(`
          Extract the progress text that shows "You've viewed X of Y items" or similar pagination information.
          Return as: { "viewed": number, "total": number, "hasMore": boolean }
        `);
        
        if (progressInfo && progressInfo.hasMore === false) {
          Logger.info('All items have been loaded according to progress indicator');
          return false;
        }
      } catch (error) {
        Logger.info('Could not extract progress information');
      }
      
      // Try to find and click the "LOAD MORE" button
      await this.stagehand.page.act('Click the "LOAD MORE" button');
      
      Logger.success('Successfully clicked "LOAD MORE" button');
      return true; // More content available
      
    } catch (error) {
      Logger.info('No more content to load or "LOAD MORE" button not found');
      return false; // No more content
    }
  }

  /**
   * Get available categories for Primark
   */
  getAvailableCategories() {
    return this.categories;
  }

  /**
   * Scrape specific Primark categories
   */
  async scrapePrimark(selectedCategories = null) {
    const categoriesToScrape = selectedCategories || this.categories.slice(0, 2); // Default to first 2 categories
    
    Logger.info(`Starting Primark scraping for categories: ${categoriesToScrape.map(c => `${c.main}>${c.sub}`).join(', ')}`);
    
    return await this.scrapeProducts(categoriesToScrape);
  }

  /**
   * Quick scrape for testing (single category)
   */
  async quickScrape() {
    Logger.info('Starting Primark quick scrape (Women > Tops and T-Shirts only)');
    return await this.scrapeProducts([{ main: 'WOMEN', sub: 'TOPS AND T-SHIRTS' }]);
  }
}
