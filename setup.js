#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';

/**
 * Setup script to help configure the project
 */
async function setup() {
  console.log('🚀 Setting up Stagehand/Browserbase Scraper POC...\n');

  try {
    // Check if .env exists
    const envExists = await fs.access('.env').then(() => true).catch(() => false);
    
    if (!envExists) {
      console.log('📝 Creating .env file from template...');
      await fs.copyFile('.env.example', '.env');
      console.log('✅ .env file created');
      console.log('⚠️  Please edit .env and add your BROWSERBASE_API_KEY');
    } else {
      console.log('✅ .env file already exists');
    }

    // Check directories
    const requiredDirs = ['data', 'screenshots', 'results'];
    for (const dir of requiredDirs) {
      try {
        await fs.access(dir);
        console.log(`✅ Directory exists: ${dir}`);
      } catch {
        await fs.mkdir(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
      }
    }

    console.log('\n🎉 Setup complete!');
    console.log('\nNext steps:');
    console.log('1. Get your Browserbase API key from: https://browserbase.com');
    console.log('2. Edit .env and add: BROWSERBASE_API_KEY=your_key_here');
    console.log('3. Run tests: npm test');
    console.log('4. Test a single site: npm run test:site -- --site=waitrose');
    console.log('5. Run all sites: npm start');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

setup();
