#!/usr/bin/env node

import { SITES } from './src/config/sites.js';
import { ScraperManager } from './src/scrapers/ScraperManager.js';

console.log('Testing API category response format...\n');

const scraperManager = new ScraperManager();

// Simulate the API response
const sites = Object.entries(SITES).map(([key, config]) => ({
  id: key,
  name: config.name,
  url: config.url,
  type: config.type,
  strategy: config.strategy,
  supported: scraperManager.isSiteSupported(key),
  categories: scraperManager.isSiteSupported(key) ? scraperManager.getAvailableCategories(key) : []
}));

console.log('=== API Response Simulation ===');
console.log(JSON.stringify({ sites }, null, 2));

console.log('\n=== Category Selection Test ===');
const waitrose = sites.find(s => s.id === 'waitrose');
if (waitrose && waitrose.categories.length > 0) {
  console.log(`✅ Waitrose has ${waitrose.categories.length} categories:`);
  waitrose.categories.forEach((cat, index) => {
    console.log(`  ${index + 1}. ${cat}`);
  });
  
  // Test category selection
  const selectedCategories = ['Fresh & Chilled', 'Frozen'];
  console.log(`\n✅ Testing category selection: ${selectedCategories.join(', ')}`);
  
  // Simulate scraping request
  const scrapingRequest = {
    site: 'waitrose',
    categories: selectedCategories,
    quickScrape: false
  };
  
  console.log('Scraping request payload:');
  console.log(JSON.stringify(scrapingRequest, null, 2));
} else {
  console.log('❌ Waitrose categories not found');
}

console.log('\n✅ API category test completed!');
