import { WaitroseScraper } from './WaitroseScraper.js';
import { NextScraper } from './NextScraper.js';
import { PrimarkScraper } from './PrimarkScraper.js';
import { TheToyShopScraper } from './TheToyShopScraper.js';
import { SITES } from '../config/sites.js';
import { Logger } from '../utils/logger.js';
import { EventEmitter } from 'events';

/**
 * Manager for coordinating all site scrapers
 */
export class ScraperManager extends EventEmitter {
  constructor() {
    super();
    this.activeJobs = new Map();
    this.scraperClasses = {
      'waitrose': WaitroseScraper,
      'next': NextScraper,
      'primark': PrimarkScraper,
      'thetoyshop': TheToyShopScraper
    };
  }

  /**
   * Get scraper class for a site
   */
  getScraperClass(siteId) {
    const scraperClass = this.scraperClasses[siteId];
    if (!scraperClass) {
      throw new Error(`No scraper implementation found for site: ${siteId}`);
    }
    return scraperClass;
  }

  /**
   * Check if site is supported
   */
  isSiteSupported(siteId) {
    return this.scraperClasses.hasOwnProperty(siteId);
  }

  /**
   * Get supported sites
   */
  getSupportedSites() {
    return Object.keys(this.scraperClasses);
  }

  /**
   * Start scraping job
   */
  async startScraping(siteId, options = {}) {
    try {
      // Validate site
      if (!this.isSiteSupported(siteId)) {
        throw new Error(`Site '${siteId}' is not supported. Supported sites: ${this.getSupportedSites().join(', ')}`);
      }

      const siteConfig = SITES[siteId];
      if (!siteConfig) {
        throw new Error(`Site configuration not found for: ${siteId}`);
      }

      // Create job ID
      const jobId = `${siteId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Create job info
      const job = {
        id: jobId,
        siteId,
        siteName: siteConfig.name,
        status: 'starting',
        startTime: new Date().toISOString(),
        progress: {
          stage: 'initializing',
          percentage: 0,
          message: 'Starting scraper...',
          productsFound: 0,
          categoriesProcessed: 0
        },
        logs: [`Starting scraping job for ${siteConfig.name}`],
        options
      };

      this.activeJobs.set(jobId, job);
      this.emit('jobUpdate', job);

      // Start scraping in background
      this.runScrapingJob(jobId, siteConfig, options).catch(error => {
        Logger.error(`Scraping job ${jobId} failed`, error);
        this.updateJobStatus(jobId, 'failed', { error: error.message });
      });

      return { jobId, message: `Started scraping ${siteConfig.name}` };

    } catch (error) {
      Logger.error(`Failed to start scraping for ${siteId}`, error);
      throw error;
    }
  }

  /**
   * Run scraping job
   */
  async runScrapingJob(jobId, siteConfig, options) {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    let scraper = null;

    try {
      // Update job status
      this.updateJobStatus(jobId, 'running', {
        progress: { stage: 'initializing', percentage: 0, message: 'Creating scraper instance...' }
      });

      // Create scraper instance
      const ScraperClass = this.getScraperClass(job.siteId);
      scraper = new ScraperClass(siteConfig);

      // Set up progress tracking
      scraper.getEventEmitter().on('progress', (progress) => {
        this.updateJobProgress(jobId, progress);
      });

      // Run scraping
      const result = await scraper.scrapeProducts(options.categories);

      // Update job with results
      this.updateJobStatus(jobId, 'completed', {
        endTime: new Date().toISOString(),
        result: {
          success: true,
          totalProducts: result.totalProducts,
          resultsPath: result.resultsPath,
          message: `Successfully scraped ${result.totalProducts} products`
        },
        progress: {
          stage: 'completed',
          percentage: 100,
          message: `Completed! Found ${result.totalProducts} products.`,
          productsFound: result.totalProducts
        }
      });

      Logger.success(`Scraping job ${jobId} completed successfully`);

    } catch (error) {
      this.updateJobStatus(jobId, 'failed', {
        endTime: new Date().toISOString(),
        error: error.message,
        progress: {
          stage: 'error',
          percentage: job.progress?.percentage || 0,
          message: `Error: ${error.message}`
        }
      });

      Logger.error(`Scraping job ${jobId} failed`, error);
    } finally {
      // Clean up scraper
      if (scraper) {
        try {
          await scraper.close();
        } catch (error) {
          Logger.warning(`Failed to close scraper for job ${jobId}`, error);
        }
      }
    }
  }

  /**
   * Update job status
   */
  updateJobStatus(jobId, status, updates = {}) {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    // Update job properties
    Object.assign(job, { status, ...updates });

    // Add log entry
    if (updates.progress?.message) {
      job.logs.push(`[${new Date().toISOString()}] ${updates.progress.message}`);
    }

    this.emit('jobUpdate', job);
  }

  /**
   * Update job progress
   */
  updateJobProgress(jobId, progress) {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    job.progress = { ...job.progress, ...progress };
    job.logs.push(`[${new Date().toISOString()}] ${progress.message}`);

    this.emit('jobUpdate', job);
  }

  /**
   * Cancel job
   */
  async cancelJob(jobId) {
    const job = this.activeJobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    if (job.status === 'running') {
      this.updateJobStatus(jobId, 'cancelled', {
        endTime: new Date().toISOString(),
        progress: {
          ...job.progress,
          stage: 'cancelled',
          message: 'Job cancelled by user'
        }
      });
    }

    return { message: 'Job cancelled' };
  }

  /**
   * Get job status
   */
  getJob(jobId) {
    return this.activeJobs.get(jobId);
  }

  /**
   * Get all jobs
   */
  getAllJobs() {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Clean up old jobs (optional)
   */
  cleanupOldJobs(maxAge = 24 * 60 * 60 * 1000) { // 24 hours
    const now = Date.now();
    const jobsToRemove = [];

    for (const [jobId, job] of this.activeJobs) {
      const jobAge = now - new Date(job.startTime).getTime();
      if (jobAge > maxAge && ['completed', 'failed', 'cancelled'].includes(job.status)) {
        jobsToRemove.push(jobId);
      }
    }

    jobsToRemove.forEach(jobId => {
      this.activeJobs.delete(jobId);
      Logger.info(`Cleaned up old job: ${jobId}`);
    });

    return jobsToRemove.length;
  }

  /**
   * Get available categories for a site
   */
  getAvailableCategories(siteId) {
    try {
      if (!this.isSiteSupported(siteId)) {
        return [];
      }

      const siteConfig = SITES[siteId];

      // First try to get categories from site configuration
      if (siteConfig.categories && Array.isArray(siteConfig.categories)) {
        return siteConfig.categories;
      }

      // Fallback to scraper instance method
      try {
        const ScraperClass = this.getScraperClass(siteId);
        const scraper = new ScraperClass(siteConfig);

        if (typeof scraper.getAvailableCategories === 'function') {
          return scraper.getAvailableCategories();
        }
      } catch (scraperError) {
        Logger.warning(`Failed to get categories from scraper for ${siteId}`, scraperError);
      }

      return [];
    } catch (error) {
      Logger.error(`Failed to get categories for ${siteId}`, error);
      return [];
    }
  }


}
