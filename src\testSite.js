#!/usr/bin/env node

import { validateConfig } from './config/browserbase.js';
import { SITES } from './config/sites.js';
import { BaseScraper } from './scrapers/BaseScraper.js';
import { Logger } from './utils/logger.js';

/**
 * Test a specific site with detailed output
 */
async function testSite() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const siteArg = args.find(arg => arg.startsWith('--site='));
    
    if (!siteArg) {
      Logger.error('Please specify a site to test: --site=<sitename>');
      Logger.info('Available sites:');
      Object.keys(SITES).forEach(key => {
        Logger.info(`  - ${key} (${SITES[key].name})`);
      });
      process.exit(1);
    }

    const siteName = siteArg.split('=')[1];
    const siteConfig = SITES[siteName];
    
    if (!siteConfig) {
      Logger.error(`Site '${siteName}' not found.`);
      Logger.info('Available sites:');
      Object.keys(SITES).forEach(key => {
        Logger.info(`  - ${key} (${SITES[key].name})`);
      });
      process.exit(1);
    }

    // Validate configuration
    validateConfig();

    Logger.info(`Testing site: ${siteConfig.name}`);
    Logger.info(`URL: ${siteConfig.url}`);
    Logger.info(`Type: ${siteConfig.type}`);

    // Create and run scraper
    const scraper = new BaseScraper(siteConfig);
    
    Logger.info('\n🚀 Starting analysis...');
    const analysis = await scraper.runAnalysis();
    
    // Display detailed results
    Logger.info('\n📊 Analysis Results:');
    Logger.info(`Title: ${analysis.title}`);
    Logger.info(`URL: ${analysis.url}`);
    
    if (analysis.meta) {
      Logger.info('\n🏷️  Meta Information:');
      Object.entries(analysis.meta).forEach(([key, value]) => {
        if (key.includes('description') || key.includes('title') || key.includes('keywords')) {
          Logger.info(`  ${key}: ${value}`);
        }
      });
    }

    if (analysis.structure) {
      Logger.info('\n🏗️  Page Structure:');
      Object.entries(analysis.structure).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        Logger.info(`  ${status} ${key}: ${value}`);
      });
    }

    if (analysis.forms && analysis.forms.length > 0) {
      Logger.info(`\n📝 Forms Found: ${analysis.forms.length}`);
      analysis.forms.forEach((form, index) => {
        Logger.info(`  Form ${index + 1}:`);
        Logger.info(`    Action: ${form.action || 'Not specified'}`);
        Logger.info(`    Method: ${form.method || 'GET'}`);
        Logger.info(`    Inputs: ${form.inputs.length}`);
      });
    }

    if (analysis.links && analysis.links.length > 0) {
      Logger.info(`\n🔗 Navigation Links: ${analysis.links.length}`);
      analysis.links.slice(0, 5).forEach(link => {
        Logger.info(`  - ${link.text} (${link.href})`);
      });
      if (analysis.links.length > 5) {
        Logger.info(`  ... and ${analysis.links.length - 5} more`);
      }
    }

    if (analysis.images && analysis.images.length > 0) {
      Logger.info(`\n🖼️  Images: ${analysis.images.length} found`);
    }

    Logger.success('\n✅ Test completed successfully!');
    Logger.info('Check the screenshots/ and results/ directories for detailed output.');

  } catch (error) {
    Logger.error('Test failed', error);
    process.exit(1);
  }
}

// Run the test
testSite();
