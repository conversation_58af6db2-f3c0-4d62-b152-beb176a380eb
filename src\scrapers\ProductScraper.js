import { BaseScraper } from './BaseScraper.js';
import { SCRAPING_CONFIG } from '../config/sites.js';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/fileUtils.js';
import { EventEmitter } from 'events';

/**
 * Base class for product scraping with progress tracking
 */
export class ProductScraper extends BaseScraper {
  constructor(siteConfig) {
    super(siteConfig);
    this.eventEmitter = new EventEmitter();
    this.products = [];
    this.currentCategory = null;
    this.progress = {
      stage: 'initializing',
      percentage: 0,
      message: 'Starting scraper...',
      productsFound: 0,
      categoriesProcessed: 0
    };
  }

  /**
   * Emit progress update
   */
  emitProgress(stage, percentage, message, data = {}) {
    this.progress = {
      stage,
      percentage: Math.min(100, Math.max(0, percentage)),
      message,
      productsFound: this.products.length,
      categoriesProcessed: this.progress.categoriesProcessed,
      ...data
    };
    
    this.eventEmitter.emit('progress', this.progress);
    Logger.info(`[${this.siteConfig.name}] ${message} (${percentage}%)`);
  }

  /**
   * Handle cookie consent - must be implemented by each site
   */
  async handleCookies() {
    throw new Error('handleCookies() must be implemented by site-specific scraper');
  }

  /**
   * Navigate to main category - must be implemented by each site
   */
  async navigateToCategory(category) {
    throw new Error('navigateToCategory() must be implemented by site-specific scraper');
  }

  /**
   * Extract products from current page - must be implemented by each site
   */
  async extractProducts() {
    throw new Error('extractProducts() must be implemented by site-specific scraper');
  }

  /**
   * Handle pagination - must be implemented by each site
   */
  async handlePagination() {
    throw new Error('handlePagination() must be implemented by site-specific scraper');
  }

  /**
   * Validate product data
   */
  validateProduct(product) {
    const required = ['name', 'price'];
    const missing = required.filter(field => !product[field]);
    
    if (missing.length > 0) {
      Logger.warning(`Product missing required fields: ${missing.join(', ')}`, product);
      return false;
    }
    
    return true;
  }

  /**
   * Add product to collection (with limit checking)
   */
  addProduct(product) {
    if (this.validateProduct(product)) {
      // Check if we've reached the product limit
      if (SCRAPING_CONFIG.testMode && this.products.length >= SCRAPING_CONFIG.maxProducts) {
        Logger.warning(`Reached product limit of ${SCRAPING_CONFIG.maxProducts} products for testing`);
        return false;
      }

      // Add metadata
      product.scrapedAt = new Date().toISOString();
      product.site = this.siteConfig.name;
      product.category = this.currentCategory;
      product.sessionId = this.sessionId;

      this.products.push(product);

      // Log progress every 10 products
      if (this.products.length % 10 === 0) {
        Logger.info(`Progress: ${this.products.length} products scraped`);
      }

      return true;
    }
    return false;
  }

  /**
   * Save results to file
   */
  async saveResults() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${this.siteConfig.name.toLowerCase().replace(/\s+/g, '_')}_products_${timestamp}.json`;
      const resultsPath = FileUtils.getResultsPath(filename);
      
      const results = {
        site: this.siteConfig,
        scrapeInfo: {
          startTime: this.startTime,
          endTime: new Date().toISOString(),
          sessionId: this.sessionId,
          totalProducts: this.products.length,
          categoriesProcessed: this.progress.categoriesProcessed
        },
        products: this.products
      };
      
      await FileUtils.saveJSON(resultsPath, results);
      Logger.success(`Results saved: ${resultsPath}`);
      
      return resultsPath;
    } catch (error) {
      Logger.error('Failed to save results', error);
      throw error;
    }
  }

  /**
   * Run complete product scraping
   */
  async scrapeProducts(categories = ['default']) {
    // Handle case where undefined is explicitly passed
    if (categories === undefined || categories === null) {
      categories = ['default'];
    }

    this.startTime = new Date().toISOString();
    
    try {
      this.emitProgress('initializing', 0, 'Initializing scraper session...');
      await this.initialize();
      
      this.emitProgress('navigating', 10, 'Navigating to site...');
      await this.navigate();
      
      this.emitProgress('cookies', 20, 'Handling cookie consent...');
      await this.handleCookies();
      
      // Process each category
      const totalCategories = categories.length;
      for (let i = 0; i < totalCategories; i++) {
        const category = categories[i];
        this.currentCategory = category;
        this.progress.categoriesProcessed = i;
        
        const categoryProgress = 30 + (i / totalCategories) * 60;
        this.emitProgress('category', categoryProgress, `Processing category: ${category}...`);
        
        await this.navigateToCategory(category);
        await this.extractProductsWithPagination();
      }
      
      this.emitProgress('saving', 95, 'Saving results...');
      const resultsPath = await this.saveResults();
      
      this.emitProgress('completed', 100, `Scraping completed! Found ${this.products.length} products.`, {
        resultsPath,
        totalProducts: this.products.length
      });
      
      return {
        success: true,
        products: this.products,
        resultsPath,
        totalProducts: this.products.length
      };
      
    } catch (error) {
      this.emitProgress('error', this.progress.percentage, `Error: ${error.message}`);
      Logger.error(`Scraping failed for ${this.siteConfig.name}`, error);
      throw error;
    } finally {
      await this.close();
    }
  }

  /**
   * Extract products with pagination handling
   */
  async extractProductsWithPagination() {
    let hasMore = true;
    let pageCount = 0;
    
    while (hasMore) {
      pageCount++;
      
      this.emitProgress('extracting', this.progress.percentage, 
        `Extracting products from page ${pageCount}...`);
      
      // Extract products from current page
      const pageProducts = await this.extractProducts();
      
      if (pageProducts && pageProducts.length > 0) {
        let addedCount = 0;
        for (const product of pageProducts) {
          if (this.addProduct(product)) {
            addedCount++;
          } else {
            // Product limit reached
            hasMore = false;
            break;
          }
        }
        Logger.info(`Extracted ${addedCount} products from page ${pageCount} (Total: ${this.products.length})`);
      } else {
        Logger.warning(`No products found on page ${pageCount}`);
      }

      // Check if we've reached the limit
      if (SCRAPING_CONFIG.testMode && this.products.length >= SCRAPING_CONFIG.maxProducts) {
        Logger.info(`Reached product limit of ${SCRAPING_CONFIG.maxProducts} products. Stopping pagination.`);
        hasMore = false;
      }

      // Try to load more if we haven't reached the limit
      if (hasMore) {
        try {
          hasMore = await this.handlePagination();
          if (hasMore) {
            // Wait for new content to load using DOM settling
            await this.stagehand.page.waitForLoadState('domcontentloaded');
          }
        } catch (error) {
          Logger.info('Pagination ended or failed', error.message);
          hasMore = false;
        }
      }
    }
    
    Logger.success(`Category completed: ${this.products.length} total products found`);
  }

  /**
   * Get event emitter for progress tracking
   */
  getEventEmitter() {
    return this.eventEmitter;
  }
}
