<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Scraper Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-spider"></i> Web Scraper Dashboard</h1>
            <div class="header-actions">
                <button onclick="testClick()" class="btn btn-secondary" style="margin-right: 10px;">
                    <i class="fas fa-bug"></i> Test JS
                </button>
                <button id="refreshBtn" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </header>

        <main class="main">
            <!-- Sites Section -->
            <section class="section">
                <h2><i class="fas fa-globe"></i> Available Sites</h2>
                <div id="sitesGrid" class="sites-grid">
                    <!-- Sites will be loaded here -->
                </div>
            </section>

            <!-- Active Jobs Section -->
            <section class="section">
                <h2><i class="fas fa-tasks"></i> Active Jobs</h2>
                <div id="jobsContainer" class="jobs-container">
                    <div class="no-jobs">
                        <i class="fas fa-clipboard-list"></i>
                        <p>No active jobs. Start scraping a site to see jobs here.</p>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="section">
                <h2><i class="fas fa-download"></i> Results</h2>
                <div id="resultsContainer" class="results-container">
                    <!-- Results will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Category Selection Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="categoryModalTitle">Select Categories</h3>
                <button class="modal-close" onclick="dashboard.closeCategoryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="categoryModalContent">
                    <p>Select the categories you want to scrape:</p>
                    <div id="categoryList" class="category-list">
                        <!-- Categories will be loaded here -->
                    </div>
                    <div class="modal-actions">
                        <button id="selectAllBtn" class="btn btn-secondary">Select All</button>
                        <button id="clearAllBtn" class="btn btn-secondary">Clear All</button>
                        <button id="startScrapingBtn" class="btn btn-primary">Start Scraping</button>
                        <button class="btn btn-secondary" onclick="dashboard.closeCategoryModal()">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Details Modal -->
    <div id="jobModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Job Details</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <!-- Job details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
