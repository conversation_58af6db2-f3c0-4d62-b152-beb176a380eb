# LLM API Setup Guide

## 🚨 Required: LLM API Key Configuration

The web scraper requires an **LLM API key** to power Stagehand's intelligent web automation features (act, extract, observe). 

### Why is this needed?
Stagehand uses Large Language Models to:
- **Understand web pages** and locate elements intelligently
- **Extract structured data** from complex layouts  
- **Navigate websites** using natural language instructions
- **Handle dynamic content** and adapt to page changes

---

## 🔧 Setup Options

### Option 1: OpenAI (Recommended)

1. **Get API Key**: Visit [OpenAI API Keys](https://platform.openai.com/api-keys)
2. **Create account** if you don't have one
3. **Generate new API key** 
4. **Update .env file**:
   ```bash
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

**Cost**: ~$0.01-0.05 per scraping session (very affordable)

### Option 2: Anthropic Claude

1. **Get API Key**: Visit [Anthropic Console](https://console.anthropic.com/)
2. **Create account** and get API key
3. **Update .env file**:
   ```bash
   ANTHROPIC_API_KEY=sk-ant-your-actual-api-key-here
   LLM_PROVIDER=anthropic
   LLM_MODEL=claude-3-haiku-********
   ```

---

## 📝 Configuration Steps

1. **Edit the .env file** in your project root
2. **Replace** `your_openai_api_key_here` with your actual API key
3. **Save the file**
4. **Restart the dashboard server**:
   ```bash
   node minimal-dashboard.js
   ```

### Example .env configuration:
```bash
# Browserbase API Key (already configured)
BROWSERBASE_API_KEY=bb_live_MRy36QffXPBqC3WccZpj5Lvul1E

# LLM API Configuration (REQUIRED)
OPENAI_API_KEY=sk-proj-abc123...your-real-key-here
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini
```

---

## ✅ Verification

After adding your API key:

1. **Restart the server**
2. **Check the console** for: `✅ LLM provider: openai (gpt-4o-mini)`
3. **Start a scraping job** - it should now work without errors!

---

## 💡 Model Recommendations

| Provider | Model | Cost | Speed | Quality |
|----------|-------|------|-------|---------|
| **OpenAI** | `gpt-4o-mini` | 💰 Cheapest | ⚡ Fast | ✅ Good |
| **OpenAI** | `gpt-4o` | 💰💰 Medium | ⚡ Fast | ✅✅ Excellent |
| **Anthropic** | `claude-3-haiku-********` | 💰 Cheap | ⚡ Fast | ✅ Good |
| **Anthropic** | `claude-3-5-sonnet-20241022` | 💰💰💰 Expensive | ⚡ Medium | ✅✅✅ Best |

**Recommendation**: Start with `gpt-4o-mini` for cost-effective scraping.

---

## 🔒 Security Notes

- **Never commit** API keys to version control
- **Keep .env file** in your .gitignore
- **Use environment variables** in production
- **Monitor usage** on your API provider dashboard

---

## 🆘 Troubleshooting

### Error: "No LLM API key configured"
- ✅ Check your .env file has the correct API key
- ✅ Restart the server after adding the key
- ✅ Verify the key format (starts with `sk-` for OpenAI)

### Error: "Invalid API key"
- ✅ Double-check the API key is correct
- ✅ Ensure you have credits/billing set up
- ✅ Try generating a new API key

### Error: "Rate limit exceeded"
- ✅ Wait a few minutes and try again
- ✅ Check your API usage limits
- ✅ Consider upgrading your API plan

---

## 🚀 Ready to Scrape!

Once configured, your scraper will be able to:
- ✅ Navigate complex websites intelligently
- ✅ Handle cookie consents automatically  
- ✅ Extract structured product data
- ✅ Adapt to different site layouts
- ✅ Provide real-time progress updates

**Happy scraping!** 🎯
