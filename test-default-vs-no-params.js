import { WaitroseScraper } from './src/scrapers/WaitroseScraper.js';
import { SITES } from './src/config/sites.js';

async function testDefaultVsNoParams() {
  console.log('🧪 Testing different parameter scenarios...\n');
  
  const siteConfig = SITES['waitrose'];
  
  try {
    console.log('🔍 Test 1: Calling scrapeProducts() with no parameters (should use default)');
    const scraper1 = new WaitroseScraper(siteConfig);
    const result1 = await scraper1.scrapeProducts();
    
    console.log('✅ Result 1:', {
      success: result1.success,
      totalProducts: result1.totalProducts,
      productsLength: result1.products?.length
    });
    
    await scraper1.close();
    
  } catch (error) {
    console.error('❌ Error with no parameters:', error.message);
  }
  
  try {
    console.log('\n🔍 Test 2: Calling scrapeProducts(undefined) - Dashboard scenario');
    const scraper2 = new WaitroseScraper(siteConfig);
    const result2 = await scraper2.scrapeProducts(undefined);
    
    console.log('✅ Result 2:', {
      success: result2.success,
      totalProducts: result2.totalProducts,
      productsLength: result2.products?.length
    });
    
    await scraper2.close();
    
  } catch (error) {
    console.error('❌ Error with undefined:', error.message);
  }
  
  try {
    console.log('\n🔍 Test 3: Calling scrapeProducts(["default"]) - Explicit default');
    const scraper3 = new WaitroseScraper(siteConfig);
    const result3 = await scraper3.scrapeProducts(['default']);
    
    console.log('✅ Result 3:', {
      success: result3.success,
      totalProducts: result3.totalProducts,
      productsLength: result3.products?.length
    });
    
    await scraper3.close();
    
  } catch (error) {
    console.error('❌ Error with ["default"]:', error.message);
  }
}

testDefaultVsNoParams();
