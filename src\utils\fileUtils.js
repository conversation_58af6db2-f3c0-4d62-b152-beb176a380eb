import fs from 'fs/promises';
import path from 'path';
import { Logger } from './logger.js';

/**
 * File utility functions
 */
export class FileUtils {
  /**
   * Save data to JSON file
   */
  static async saveJSON(filePath, data) {
    try {
      const dir = path.dirname(filePath);
      await fs.mkdir(dir, { recursive: true });
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      Logger.success(`Data saved to ${filePath}`);
    } catch (error) {
      Logger.error(`Failed to save JSON to ${filePath}`, error);
      throw error;
    }
  }

  /**
   * Load data from JSON file
   */
  static async loadJSON(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      Logger.error(`Failed to load JSON from ${filePath}`, error);
      throw error;
    }
  }

  /**
   * Save screenshot with timestamp
   */
  static async saveScreenshot(buffer, siteName, description = '') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${siteName}_${description}_${timestamp}.png`;
      const filePath = path.join('screenshots', filename);
      
      await fs.mkdir('screenshots', { recursive: true });
      await fs.writeFile(filePath, buffer);
      
      Logger.success(`Screenshot saved: ${filePath}`);
      return filePath;
    } catch (error) {
      Logger.error('Failed to save screenshot', error);
      throw error;
    }
  }

  /**
   * Generate results file path
   */
  static getResultsPath(siteName, type = 'analysis') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return path.join('results', `${siteName}_${type}_${timestamp}.json`);
  }

  /**
   * Check if file exists
   */
  static async exists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
