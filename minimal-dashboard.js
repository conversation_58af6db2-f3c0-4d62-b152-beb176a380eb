#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import { SITES } from './src/config/sites.js';
import { ScraperManager } from './src/scrapers/ScraperManager.js';
import { readFileSync, existsSync, readdirSync, statSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Creating Express app...');
const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });
const port = 3002;

// Job management
const activeJobs = new Map();
let jobCounter = 0;

// Scraper manager
const scraperManager = new ScraperManager();

console.log('Setting up middleware...');
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'src', 'dashboard', 'public')));

console.log('Setting up WebSocket...');
wss.on('connection', (ws) => {
  console.log('Dashboard client connected');

  // Send current job status
  ws.send(JSON.stringify({
    type: 'jobs_update',
    data: scraperManager.getAllJobs()
  }));

  ws.on('close', () => {
    console.log('Dashboard client disconnected');
  });
});

// Set up scraper manager event handling
scraperManager.on('jobUpdate', (job) => {
  // Sync with local activeJobs for compatibility
  activeJobs.set(job.id, job);
  broadcastJobUpdate();
});

function broadcastJobUpdate() {
  const jobs = scraperManager.getAllJobs();
  const message = JSON.stringify({
    type: 'jobs_update',
    data: jobs
  });

  wss.clients.forEach(client => {
    if (client.readyState === 1) { // WebSocket.OPEN
      client.send(message);
    }
  });
}

console.log('Setting up routes...');

// API Routes
app.get('/api/sites', (req, res) => {
  console.log('API call: /api/sites');
  const sites = Object.entries(SITES).map(([key, config]) => ({
    id: key,
    name: config.name,
    url: config.url,
    type: config.type,
    strategy: config.strategy,
    supported: scraperManager.isSiteSupported(key),
    categories: scraperManager.isSiteSupported(key) ? scraperManager.getAvailableCategories(key) : []
  }));

  res.json({ sites });
});

app.get('/api/sites/:site/categories', (req, res) => {
  const { site } = req.params;
  console.log(`API call: /api/sites/${site}/categories`);

  if (!scraperManager.isSiteSupported(site)) {
    return res.status(404).json({
      error: `Site '${site}' is not supported for scraping`,
      supportedSites: scraperManager.getSupportedSites()
    });
  }

  const categories = scraperManager.getAvailableCategories(site);
  res.json({ site, categories });
});

app.post('/api/scrape/:site', async (req, res) => {
  const { site } = req.params;
  const { categories, quickScrape } = req.body || {};

  console.log(`API call: /api/scrape/${site}`, { categories, quickScrape });

  try {
    // Check if site is supported
    if (!scraperManager.isSiteSupported(site)) {
      const supportedSites = scraperManager.getSupportedSites();
      return res.status(404).json({
        error: `Site '${site}' is not supported for scraping. Supported sites: ${supportedSites.join(', ')}`,
        supportedSites
      });
    }

    const siteConfig = SITES[site];
    if (!siteConfig) {
      return res.status(404).json({
        error: `Site configuration not found for '${site}'`
      });
    }

    // Prepare scraping options
    const options = {
      categories: categories || (quickScrape ? ['default'] : undefined),
      quickScrape: quickScrape || false
    };

    // Start real scraping job
    const result = await scraperManager.startScraping(site, options);

    res.json(result);

  } catch (error) {
    console.error(`Failed to start scraping for ${site}:`, error);
    res.status(500).json({
      error: error.message,
      details: 'Failed to start scraping job'
    });
  }
});

// Real scraping is now handled by ScraperManager

app.get('/api/jobs', (req, res) => {
  console.log('API call: /api/jobs');
  const jobs = scraperManager.getAllJobs();
  res.json({ jobs });
});

app.get('/api/jobs/:jobId', (req, res) => {
  const { jobId } = req.params;
  const job = scraperManager.getJob(jobId);

  if (!job) {
    return res.status(404).json({ error: 'Job not found' });
  }

  res.json({ job });
});

app.delete('/api/jobs/:jobId', async (req, res) => {
  const { jobId } = req.params;

  try {
    const result = await scraperManager.cancelJob(jobId);
    res.json(result);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

app.get('/api/results', (req, res) => {
  console.log('API call: /api/results');
  try {
    const resultsDir = path.join(process.cwd(), 'results');
    if (!existsSync(resultsDir)) {
      return res.json({ files: [] });
    }

    const files = readdirSync(resultsDir)
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(resultsDir, file);
        const stats = statSync(filePath);
        return {
          name: file,
          size: stats.size,
          modified: stats.mtime.toISOString()
        };
      });

    res.json({ files });
  } catch (error) {
    res.status(500).json({ error: 'Failed to read results directory' });
  }
});

app.get('/api/results/:filename', (req, res) => {
  const { filename } = req.params;
  const filePath = path.join(process.cwd(), 'results', filename);

  if (!existsSync(filePath)) {
    return res.status(404).json({ error: 'File not found' });
  }

  res.download(filePath);
});

// Serve debug test
app.get('/debug', (req, res) => {
  console.log('Serving debug test');
  res.sendFile(path.join(__dirname, 'debug-test.html'));
});

// Serve dashboard
app.get('/', (req, res) => {
  console.log('Serving dashboard index.html');
  res.sendFile(path.join(__dirname, 'src', 'dashboard', 'public', 'index.html'));
});

console.log('Starting server...');
server.listen(port, () => {
  console.log(`🚀 Dashboard server running at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('  - Dashboard: http://localhost:' + port);
  console.log('  - API: http://localhost:' + port + '/api/sites');
  console.log('  - WebSocket: ws://localhost:' + port);
});
