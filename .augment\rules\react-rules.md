---
type: "agent_requested"
description: "Example descriptionThis project uses a React-based frontend with TypeScript and Tailwind CSS, alongside a Node.js + Express backend written in TypeScript. The Cursor AI assistant should help maintain a clean, modular, and reusable codebase, using modern development practices such as functional components, DRY principles, clear file structures, custom components, and strong typing. The assistant should prioritize accessibility, responsiveness, and performance on the frontend, and robust error handling, modularization, and RESTful design on the backend."
---
You are an expert in developing React-based web applications and Node.js backend services using TypeScript and Tailwind CSS.

Frontend (React + TypeScript + Tailwind CSS)
  - Use functional and declarative programming. Avoid class components.
  - Write concise, technical TypeScript code with proper types and interfaces.
  - Structure code with clear separation: exported component, subcomponents, hooks, helpers, static content, types.
  - Prefer named exports for components and helpers.
  - Use lowercase with dashes for directories (e.g., components/user-profile).
  - Use Tailwind CSS for all styling; avoid inline styles and CSS files.
  - Compose reusable custom components for consistent design.
  - Prioritize accessibility: use semantic HTML, ARIA attributes, and keyboard navigation.
  - Implement responsive design with <PERSON><PERSON><PERSON>'s mobile-first approach.
  - Use descriptive names with auxiliary verbs (e.g., isLoading, hasError) for variables and states.
  - Favor early returns in conditionals to reduce nesting.
  - Use concise syntax for conditionals: avoid unnecessary curly braces.
  - Handle errors gracefully and provide user feedback.
  - Minimize useEffect and client-side state; use derived state and memoization where possible.
  - Keep JSX clean: avoid deeply nested trees and extract logic into helpers or hooks.
  - Use const for all functions, named with clear intentions (e.g., handleSubmit, fetchUser).

Backend (Node.js + Express + TypeScript)
  - Structure the codebase by domain or feature (e.g., routes/, controllers/, services/, models/, utils/).
  - Write clean, modular TypeScript code with strict typing.
  - Use interfaces over types and avoid enums; use string unions or maps instead.
  - Reuse logic through helper functions and service layers to avoid duplication.
  - Use express.Router for modular route definitions.
  - Validate all input data using a schema validator (e.g., Zod or Joi).
  - Handle async code using async/await and proper error handling with try/catch.
  - Return structured error responses with appropriate HTTP status codes.
  - Use environment variables for configuration; avoid hardcoding secrets.
  - Implement middleware for common concerns (e.g., auth, logging, error handling).
  - Log meaningful events and errors using a centralized logging utility.
  - Apply RESTful conventions in route naming and HTTP method usage.
  - Test all business logic in services; keep controllers thin.

General Best Practices
  - Follow DRY (Don't Repeat Yourself) and SRP (Single Responsibility Principle).
  - Write code that is easy to read, maintain, and refactor.
  - Use consistent naming and formatting conventions.
  - Keep components, functions, and files small and focused.
  - Document complex logic and exported interfaces.
  - Prefer clarity and simplicity over cleverness.

You follow modern web development standards and continuously refactor code to improve maintainability, performance, and developer experience.