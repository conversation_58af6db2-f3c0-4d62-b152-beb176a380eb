import { Stagehand } from '@browserbasehq/stagehand';
import { BROWSERBASE_CONFIG } from '../config/browserbase.js';
import { SCRAPING_CONFIG } from '../config/sites.js';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/fileUtils.js';

/**
 * Base scraper class for all site scrapers
 */
export class BaseScraper {
  constructor(siteConfig) {
    this.siteConfig = siteConfig;
    this.stagehand = null;
    this.sessionId = null;
  }

  /**
   * Initialize Stagehand session
   */
  async initialize() {
    try {
      Logger.info(`Initializing scraper for ${this.siteConfig.name}`);
      
      // Debug configuration
      console.log('🔧 Stagehand config:', {
        browserMode: BROWSERBASE_CONFIG.browserMode,
        localHeadless: BROWSERBASE_CONFIG.localHeadless,
        apiKey: BROWSERBASE_CONFIG.apiKey ? 'Set' : 'Missing',
        projectId: BROWSERBASE_CONFIG.projectId ? 'Set' : 'Missing',
        llmApiKey: BROWSERBASE_CONFIG.llm.apiKey ? 'Set' : 'Missing',
        llmProvider: BROWSERBASE_CONFIG.llm.provider,
        llmModel: BROWSERBASE_CONFIG.llm.model
      });

      // Set environment variables for Stagehand
      process.env.OPENAI_API_KEY = BROWSERBASE_CONFIG.llm.apiKey;

      // Configure Stagehand based on browser mode
      const stagehandConfig = {
        env: BROWSERBASE_CONFIG.browserMode,
        ...BROWSERBASE_CONFIG.sessionConfig
      };

      // Add Browserbase-specific config
      if (BROWSERBASE_CONFIG.browserMode === 'BROWSERBASE') {
        stagehandConfig.apiKey = BROWSERBASE_CONFIG.apiKey;
        stagehandConfig.projectId = BROWSERBASE_CONFIG.projectId;
        stagehandConfig.headless = true; // Always headless for cloud
      } else {
        // Local browser configuration
        stagehandConfig.headless = BROWSERBASE_CONFIG.localHeadless;
      }

      this.stagehand = new Stagehand(stagehandConfig);

      await this.stagehand.init();
      this.sessionId = this.stagehand.sessionId;

      // Log the browser mode being used
      const actualEnv = this.stagehand.intEnv || BROWSERBASE_CONFIG.browserMode;
      if (actualEnv === 'BROWSERBASE') {
        Logger.success(`☁️  Browserbase session initialized: ${this.sessionId}`);
      } else {
        const headlessStatus = BROWSERBASE_CONFIG.localHeadless ? 'headless' : 'visible';
        Logger.success(`💻 Local browser initialized (${headlessStatus}): ${this.sessionId || 'local-session'}`);
      }
      return true;
    } catch (error) {
      Logger.error('Failed to initialize scraper', error);
      throw error;
    }
  }

  /**
   * Navigate to the target site
   */
  async navigate() {
    try {
      Logger.info(`Navigating to ${this.siteConfig.url}`);
      await this.stagehand.page.goto(this.siteConfig.url, {
        waitUntil: 'networkidle',
        timeout: SCRAPING_CONFIG.timeout
      });

      // Wait for page to be fully loaded and stable
      await this.stagehand.page.waitForLoadState('domcontentloaded');
      await this.stagehand.page.waitForLoadState('networkidle');
      
      Logger.success(`Successfully navigated to ${this.siteConfig.name}`);
      return true;
    } catch (error) {
      Logger.error(`Failed to navigate to ${this.siteConfig.url}`, error);
      throw error;
    }
  }

  /**
   * Take a screenshot of the current page
   */
  async takeScreenshot(description = 'homepage') {
    try {
      const screenshot = await this.stagehand.page.screenshot({
        fullPage: true,
        type: 'png'
      });
      
      const filePath = await FileUtils.saveScreenshot(
        screenshot, 
        this.siteConfig.name.toLowerCase().replace(/\s+/g, '_'),
        description
      );
      
      return filePath;
    } catch (error) {
      Logger.error('Failed to take screenshot', error);
      throw error;
    }
  }

  /**
   * Analyze page structure
   */
  async analyzeStructure() {
    try {
      Logger.info(`Analyzing structure of ${this.siteConfig.name}`);
      
      const analysis = await this.stagehand.page.evaluate(() => {
        const result = {
          title: document.title,
          url: window.location.href,
          meta: {},
          structure: {},
          forms: [],
          links: [],
          images: []
        };

        // Get meta information
        const metaTags = document.querySelectorAll('meta');
        metaTags.forEach(meta => {
          const name = meta.getAttribute('name') || meta.getAttribute('property');
          const content = meta.getAttribute('content');
          if (name && content) {
            result.meta[name] = content;
          }
        });

        // Analyze main structure
        result.structure = {
          hasHeader: !!document.querySelector('header, .header, #header'),
          hasNav: !!document.querySelector('nav, .nav, .navigation'),
          hasMain: !!document.querySelector('main, .main, #main'),
          hasFooter: !!document.querySelector('footer, .footer, #footer'),
          hasSidebar: !!document.querySelector('aside, .sidebar, .side-nav')
        };

        // Get forms
        const forms = document.querySelectorAll('form');
        forms.forEach((form, index) => {
          const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
            type: input.type || input.tagName.toLowerCase(),
            name: input.name,
            id: input.id,
            placeholder: input.placeholder
          }));
          
          result.forms.push({
            index,
            action: form.action,
            method: form.method,
            inputs
          });
        });

        // Get navigation links
        const navLinks = document.querySelectorAll('nav a, .nav a, .navigation a');
        navLinks.forEach(link => {
          result.links.push({
            text: link.textContent.trim(),
            href: link.href,
            isExternal: !link.href.includes(window.location.hostname)
          });
        });

        // Get images
        const images = document.querySelectorAll('img');
        result.images = Array.from(images).slice(0, 10).map(img => ({
          src: img.src,
          alt: img.alt,
          width: img.width,
          height: img.height
        }));

        return result;
      });

      Logger.success(`Structure analysis completed for ${this.siteConfig.name}`);
      return analysis;
    } catch (error) {
      Logger.error('Failed to analyze page structure', error);
      throw error;
    }
  }

  /**
   * Close the session
   */
  async close() {
    try {
      if (this.stagehand) {
        await this.stagehand.close();
        Logger.info(`Session closed for ${this.siteConfig.name}`);
      }
    } catch (error) {
      Logger.error('Failed to close session', error);
    }
  }

  /**
   * Run complete analysis
   */
  async runAnalysis() {
    try {
      await this.initialize();
      await this.navigate();
      
      // Take initial screenshot
      await this.takeScreenshot('initial');
      
      // Analyze structure
      const analysis = await this.analyzeStructure();
      
      // Save results
      const resultsPath = FileUtils.getResultsPath(
        this.siteConfig.name.toLowerCase().replace(/\s+/g, '_'),
        'structure'
      );
      
      await FileUtils.saveJSON(resultsPath, {
        site: this.siteConfig,
        analysis,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId
      });

      return analysis;
    } catch (error) {
      Logger.error(`Analysis failed for ${this.siteConfig.name}`, error);
      throw error;
    } finally {
      await this.close();
    }
  }
}
