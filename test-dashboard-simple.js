#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { SITES } from './src/config/sites.js';
import { ScraperManager } from './src/scrapers/ScraperManager.js';

const app = express();
const port = 3003;

// Scraper manager
const scraperManager = new ScraperManager();

app.use(cors());
app.use(express.json());

// API Routes
app.get('/api/sites', (req, res) => {
  console.log('API call: /api/sites');
  const sites = Object.entries(SITES).map(([key, config]) => ({
    id: key,
    name: config.name,
    url: config.url,
    type: config.type,
    strategy: config.strategy,
    supported: scraperManager.isSiteSupported(key),
    categories: scraperManager.isSiteSupported(key) ? scraperManager.getAvailableCategories(key) : []
  }));

  res.json({ sites });
});

app.get('/api/sites/:site/categories', (req, res) => {
  const { site } = req.params;
  console.log(`API call: /api/sites/${site}/categories`);

  if (!scraperManager.isSiteSupported(site)) {
    return res.status(404).json({
      error: `Site '${site}' is not supported for scraping`,
      supportedSites: scraperManager.getSupportedSites()
    });
  }

  const categories = scraperManager.getAvailableCategories(site);
  res.json({ site, categories });
});

app.post('/api/scrape/:site', async (req, res) => {
  const { site } = req.params;
  const { categories, quickScrape } = req.body || {};

  console.log(`API call: /api/scrape/${site}`, { categories, quickScrape });

  try {
    // Check if site is supported
    if (!scraperManager.isSiteSupported(site)) {
      const supportedSites = scraperManager.getSupportedSites();
      return res.status(404).json({
        error: `Site '${site}' is not supported for scraping. Supported sites: ${supportedSites.join(', ')}`,
        supportedSites
      });
    }

    const siteConfig = SITES[site];
    if (!siteConfig) {
      return res.status(404).json({
        error: `Site configuration not found for '${site}'`
      });
    }

    // For testing, just return success without actually scraping
    res.json({
      success: true,
      message: `Would start scraping ${siteConfig.name} with categories: ${categories ? categories.join(', ') : 'default'}`,
      site: siteConfig.name,
      categories: categories || ['default'],
      quickScrape: quickScrape || false
    });

  } catch (error) {
    console.error(`Failed to start scraping for ${site}:`, error);
    res.status(500).json({
      error: error.message,
      details: 'Failed to start scraping job'
    });
  }
});

app.listen(port, () => {
  console.log(`🚀 Test Dashboard server running at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log(`  - Sites API: http://localhost:${port}/api/sites`);
  console.log(`  - Categories API: http://localhost:${port}/api/sites/waitrose/categories`);
  console.log(`  - Scrape API: POST http://localhost:${port}/api/scrape/waitrose`);
});
