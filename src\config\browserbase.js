import dotenv from 'dotenv';

dotenv.config();

/**
 * Browserbase configuration
 */
export const BROWSERBASE_CONFIG = {
  apiKey: process.env.BROWSERBASE_API_KEY,
  projectId: process.env.BROWSERBASE_PROJECT_ID,
  sessionTimeout: parseInt(process.env.BROWSERBASE_SESSION_TIMEOUT) || 300000,

  // Browser Mode Configuration
  browserMode: process.env.BROWSER_MODE || 'BROWSERBASE', // 'BROWSERBASE' or 'LOCAL'
  localHeadless: process.env.LOCAL_HEADLESS === 'true', // true/false for local browser visibility

  // LLM Configuration for Stagehand
  llm: {
    apiKey: process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY,
    provider: process.env.LLM_PROVIDER || 'openai', // 'openai' or 'anthropic'
    model: process.env.LLM_MODEL || 'gpt-4o' // Optimized for complex web scraping tasks
  },

  // Default session configuration
  sessionConfig: {
    browserSettings: {
      viewport: { width: 1920, height: 1080 },
      locale: 'en-GB',
      timezone: 'Europe/London'
    },
    proxies: false,
    stealth: true
  }
};

/**
 * Validate Browserbase configuration
 */
export function validateConfig() {
  if (!BROWSERBASE_CONFIG.apiKey) {
    throw new Error('BROWSERBASE_API_KEY is required. Please set it in your .env file.');
  }

  if (!BROWSERBASE_CONFIG.llm.apiKey) {
    throw new Error('LLM API key is required. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY in your .env file.');
  }

  console.log('✅ Browserbase configuration validated');
  console.log(`✅ LLM provider: ${BROWSERBASE_CONFIG.llm.provider} (${BROWSERBASE_CONFIG.llm.model})`);
  return true;
}
