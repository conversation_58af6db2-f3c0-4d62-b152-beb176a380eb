import { WaitroseScraper } from './src/scrapers/WaitroseScraper.js';
import { SITES } from './src/config/sites.js';

async function testDashboardIntegration() {
  console.log('🧪 Testing exact dashboard integration scenario...\n');
  
  // Get the exact siteConfig that ScraperManager uses
  const siteConfig = SITES['waitrose'];
  console.log('📋 Site config:', {
    name: siteConfig.name,
    url: siteConfig.url,
    type: siteConfig.type
  });
  
  // Create scraper exactly like ScraperManager does
  const scraper = new WaitroseScraper(siteConfig);
  
  try {
    console.log('\n🔍 Test 1: Calling scrapeProducts(undefined) - Dashboard scenario');
    const result1 = await scraper.scrapeProducts(undefined);
    
    console.log('✅ Result 1:', {
      success: result1.success,
      totalProducts: result1.totalProducts,
      productsLength: result1.products?.length,
      resultsPath: result1.resultsPath
    });
    
    if (result1.products && result1.products.length > 0) {
      console.log('\n📦 First few products:');
      result1.products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ${product.price}`);
      });
    } else {
      console.log('\n❌ No products found with undefined categories!');
    }
    
  } catch (error) {
    console.error('❌ Error with undefined categories:', error.message);
  }
  
  try {
    console.log('\n🔍 Test 2: Calling scrapeProducts() - Default parameter scenario');
    const result2 = await scraper.scrapeProducts();
    
    console.log('✅ Result 2:', {
      success: result2.success,
      totalProducts: result2.totalProducts,
      productsLength: result2.products?.length,
      resultsPath: result2.resultsPath
    });
    
    if (result2.products && result2.products.length > 0) {
      console.log('\n📦 First few products:');
      result2.products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ${product.price}`);
      });
    } else {
      console.log('\n❌ No products found with default categories!');
    }
    
  } catch (error) {
    console.error('❌ Error with default categories:', error.message);
  } finally {
    try {
      await scraper.close();
      console.log('\n🔒 Scraper closed successfully');
    } catch (error) {
      console.error('⚠️ Error closing scraper:', error.message);
    }
  }
}

testDashboardIntegration();
