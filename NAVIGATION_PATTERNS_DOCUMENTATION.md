# 🧭 **Website Navigation Patterns Documentation**

## **Overview**
This document provides detailed navigation patterns, popup handling, and category-to-product paths for each successfully analyzed website. This information is crucial for implementing automated Stagehand navigation.

---

## **1. 🛒 Waitrose (Grocery)**
**URL:** `https://www.waitrose.com/`

### **Initial Popups/Banners:**
- **Cookie Consent Dialog** - MUST be handled first
  - Text: "Our website uses cookies to analyse traffic..."
  - **Action Required:** Click "Allow all" button
  - **Selector Strategy:** Look for button with text "Allow all"

### **Navigation Structure:**
```
Homepage → GROCERIES → Category → Subcategory → Products → Individual Product
```

### **Main Categories:**
- GROCERIES (primary entry point)
- OFFERS
- SUMMER
- ENTERTAINING
- RECIPES
- CELLAR
- GARDEN

### **Groceries Subcategories:**
- Fresh & Chilled
- Frozen
- Bakery
- Food Cupboard
- Beer, Wine & Spirits
- Tea, Coffee & Soft Drinks
- Household
- Toiletries, Health & Beauty
- Baby & Toddler
- Pet
- Home

### **Example Navigation Path:**
1. Accept cookies: Click "Allow all"
2. Click "GROCERIES" 
3. Click "Fresh & Chilled"
4. Click "Fresh Fruit"
5. Product cards appear with clickable links
6. Click product name/image to reach product page

### **Product Page Elements:**
- Product name, price, description
- "Add to trolley" functionality
- Price per unit information

### **Key Selectors:**
- Cookie accept: Button with "Allow all" text
- Main nav: `nav` element with "GROCERIES" link
- Product cards: `article` elements with product links
- Product links: Links with "view product details" or product name

---

## **2. 👗 Next (Fashion)**
**URL:** `https://www.next.co.uk/`

### **Initial Popups/Banners:**
- **Cookie Consent Dialog** - MUST be handled first
  - Text: "OUR SITE USES COOKIES"
  - **Action Required:** Click "ACCEPT ALL COOKIES" button
  - **Alternative:** "REJECT NON-ESSENTIAL COOKIES" or "MANUALLY MANAGE COOKIES"

### **Navigation Structure:**
```
Homepage → Category (WOMEN/MEN/etc.) → Subcategory → Products → Individual Product
```

### **Main Categories:**
- WOMEN
- MEN  
- BOYS
- GIRLS
- HOME
- SCHOOL
- BABY
- FURNITURE
- HOLIDAY
- BRANDS
- BEAUTY
- GIFTS
- SPORTS
- CLEARANCE

### **Women's Subcategories (Example):**
- New In
- All Clothing
- Dresses (with further sub-types: Maxi, Midi, Mini, etc.)
- Tops & T-Shirts
- Trousers
- Jeans
- Jackets & Coats
- Footwear
- Accessories
- Lingerie
- Nightwear

### **Example Navigation Path:**
1. Accept cookies: Click "ACCEPT ALL COOKIES"
2. Click "WOMEN"
3. Click "Dresses" 
4. Browse product grid
5. Click individual product

### **Key Selectors:**
- Cookie accept: Button with "ACCEPT ALL COOKIES" text
- Main nav: Header navigation with category links
- Category menus: Dropdown/mega menus on hover/click

---

## **3. 👕 Primark (Fashion)**
**URL:** `https://www.primark.com/`

### **Initial Popups/Banners:**
- **Store Finder Banner** - May appear at top
- **Cookie Consent Dialog** - MUST be handled
  - Text: "We Use Cookies"
  - **Action Required:** Click "ACCEPT ALL COOKIES" button
  - **Alternative:** "ONLY REQUIRED COOKIES" or "Cookies Settings"

### **Navigation Structure:**
```
Homepage → Category → Subcategory → Product Collections
```

### **Main Categories:**
- WOMEN
- MEN
- KIDS
- BABY
- HOME
- BEAUTY
- HELP

### **Women's Subcategories:**
- SWEATERS AND CARDIGANS
- COATS AND JACKETS
- JEANS
- HOODIES AND SWEATSHIRTS
- JOGGERS
- TOPS AND T-SHIRTS
- SLEEPWEAR COLLECTIONS
- SHOES

### **Example Navigation Path:**
1. Accept cookies: Click "ACCEPT ALL COOKIES"
2. Click "WOMEN"
3. Click specific category (e.g., "TOPS AND T-SHIRTS")
4. Browse individual products with prices
5. Click "LOAD MORE" to see additional products

### **Key Selectors:**
- Cookie accept: Button with "ACCEPT ALL COOKIES" text
- Main nav: Header with category links
- Store banner close: Look for "Close" button if banner appears

---

## **4. 🧸 The Toy Shop (Toys)**
**URL:** `https://www.thetoyshop.com/`

### **Initial Popups/Banners:**
- **Privacy/Cookie Consent Dialog** - MUST be handled
  - Text: "We value your privacy"
  - **Action Required:** Click "Accept All Cookies" button
  - **Alternatives:** "Required cookies only" or "Manage Preferences"

### **Navigation Structure:**
```
Homepage → Navigation Category → Subcategory → Products → Individual Product
```

### **Main Navigation Categories:**
- Brands
- Type of Toy
- Outdoor
- Shop By Age
- Offers
- New Toys
- Discover
- Clearance

### **Example Navigation Path:**
1. Accept cookies: Click "Accept All Cookies"
2. Click "Type of Toy" or "Shop By Age"
3. Select specific category
4. Browse products
5. Click individual product

### **Key Selectors:**
- Cookie accept: Button with "Accept All Cookies" text
- Main nav: Header navigation menu
- Category links: Navigation menu items

---

## **5. ⚠️ Blocked/Protected Sites**

### **Hamleys (Toys)**
**URL:** `https://www.hamleys.com/`
**Status:** ❌ **BLOCKED**
- **Error:** "You have been blocked" - Anti-bot protection active
- **Recommendation:** Requires advanced bypass strategies or alternative approach

---

## **� Pagination Patterns**

### **1. 🛒 Waitrose - "Load More" Button**
- **Pattern:** Progressive loading with "Load more..." button
- **Implementation:**
  - Look for button with text "Load more..."
  - Click to append additional products to current page
  - Button remains available until all products loaded
  - **Stagehand Action:** `await page.act("Click the Load more button");`

### **2. 👗 Next - Infinite Scroll/Dynamic Loading**
- **Pattern:** Appears to use infinite scroll or dynamic loading
- **Implementation:**
  - Products load as user scrolls down
  - No visible pagination controls observed
  - **Stagehand Action:** `await page.act("Scroll down to load more products");`

### **3. 👕 Primark - "LOAD MORE" Button with Product Count**
- **Pattern:** Progressive loading with "LOAD X MORE" button showing specific quantities
- **Implementation:**
  - Navigate to category pages (e.g., TOPS AND T-SHIRTS, JEANS)
  - Shows progress: "You've viewed X of Y items"
  - Click "LOAD 24 MORE" or "LOAD X MORE" to load additional products
  - Button text changes based on remaining items (e.g., "LOAD 22 MORE")
  - **Stagehand Action:** `await page.act("Click the LOAD MORE button");`

### **4. 🧸 The Toy Shop - Traditional Numbered Pagination**
- **Pattern:** Classic numbered pagination with Previous/Next buttons
- **Implementation:**
  - Numbered page links (1, 2, 3, 4, 5...)
  - "Next Page" and "Previous Page" buttons
  - Current page highlighted
  - **Stagehand Actions:**
    - `await page.act("Click on Next Page link");`
    - `await page.act("Click on page 3 link");`
    - `await page.act("Click on Previous Page link");`

### **Pagination Detection Strategy:**
```javascript
// Check for different pagination types
const paginationTypes = [
  { type: 'load-more', selector: 'button:contains("Load more")' },
  { type: 'next-page', selector: 'a:contains("Next Page")' },
  { type: 'numbered', selector: 'a:contains("Page 2")' },
  { type: 'infinite-scroll', action: 'scroll-down' }
];
```

---

## **�🔧 Implementation Guidelines for Stagehand**

### **General Pattern:**
1. **Always handle cookies first** - Every site requires cookie consent
2. **Wait for page load** - Allow time for dynamic content
3. **Navigate hierarchically** - Follow the category → subcategory → product pattern
4. **Handle dynamic content** - Many sites use JavaScript for navigation

### **Common Selectors to Look For:**
- Cookie buttons: Text containing "Accept", "Allow", "OK"
- Navigation menus: `nav`, `header`, or elements with "menu" classes
- Product cards: `article`, elements with "product" classes
- Category links: Links in navigation areas

### **Error Handling:**
- **Timeouts:** Some sites may be slow to load
- **Dynamic content:** Wait for elements to appear
- **Blocked access:** Implement fallback strategies
- **Layout changes:** Sites may update their structure

### **Recommended Stagehand Actions:**
```javascript
// 1. Handle cookies
await page.act("Click the accept cookies button");

// 2. Navigate to category
await page.act("Click on [CATEGORY] navigation link");

// 3. Navigate to subcategory
await page.act("Click on [SUBCATEGORY] link");

// 4. Access products
await page.act("Click on product card or product link");

// 5. Handle pagination (site-specific)
// Waitrose:
await page.act("Click the Load more button");

// Primark:
await page.act("Click the LOAD MORE button");

// The Toy Shop:
await page.act("Click on Next Page link");
// or
await page.act("Click on page 2 link");

// Next:
await page.act("Scroll down to load more products");
```

---

## **📊 Summary**

| Site | Cookie Handling | Main Categories | Navigation Depth | Pagination Type | Complexity |
|------|----------------|-----------------|------------------|-----------------|------------|
| Waitrose | ✅ Required | 7 main | 4 levels | Load More Button | Low |
| Next | ✅ Required | 14 main | 3-4 levels | Infinite Scroll | Medium |
| Primark | ✅ Required | 7 main | 3 levels | Load More Button | Low |
| The Toy Shop | ✅ Required | 8 main | 3-4 levels | Numbered Pages | Medium |
| Hamleys | ❌ Blocked | N/A | N/A | N/A | High |

**Key Requirements:**
- **Cookie consent handling** is required as the first step for all accessible sites
- **Pagination handling** varies by site and must be implemented according to each site's pattern
- **The Toy Shop** offers the most robust pagination with numbered pages and next/previous controls
- **Waitrose** uses progressive loading that's easy to automate with "Load more" clicks

---

## **🚀 Quick Pagination Implementation Guide**

### **For Comprehensive Product Scraping:**

```javascript
// Waitrose - Load More Pattern
async function scrapeWaitrose(page) {
  let hasMore = true;
  while (hasMore) {
    // Extract current page products
    const products = await page.extract("Extract all product information");

    // Try to load more
    try {
      await page.act("Click the Load more button", {
        domSettleTimeoutMs: 3000 // Wait for new products to load
      });
    } catch (error) {
      hasMore = false; // No more products to load
    }
  }
}

// Primark - Load More with Progress Pattern
async function scrapePrimark(page) {
  let hasMore = true;
  while (hasMore) {
    // Extract current page products
    const products = await page.extract("Extract all product information");

    // Check progress and try to load more
    try {
      const progress = await page.extract("Extract progress text showing 'You've viewed X of Y items'");
      await page.act("Click the LOAD MORE button", {
        domSettleTimeoutMs: 3000 // Wait for new products to load
      });
    } catch (error) {
      hasMore = false; // No more products to load
    }
  }
}

// The Toy Shop - Numbered Pages Pattern
async function scrapeAllPages(page) {
  let currentPage = 1;
  let hasNextPage = true;

  while (hasNextPage) {
    // Extract current page products
    const products = await page.extract("Extract all product information");

    // Try to go to next page
    try {
      await page.act("Click on Next Page link", {
        domSettleTimeoutMs: 3000 // Wait for page to load
      });
      currentPage++;
    } catch (error) {
      hasNextPage = false; // No more pages
    }
  }
}

// Next - Infinite Scroll Pattern
async function scrapeWithScroll(page) {
  let previousHeight = 0;
  let currentHeight = await page.evaluate(() => document.body.scrollHeight);

  while (currentHeight > previousHeight) {
    // Extract visible products
    const products = await page.extract("Extract all visible product information");

    // Scroll to load more
    previousHeight = currentHeight;
    await page.act("Scroll down to load more products");
    await page.waitForTimeout(3000);
    currentHeight = await page.evaluate(() => document.body.scrollHeight);
  }
}
```

This pagination documentation ensures comprehensive product scraping across all accessible sites! 🎯
