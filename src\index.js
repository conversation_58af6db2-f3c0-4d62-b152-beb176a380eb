#!/usr/bin/env node

import { validateConfig } from './config/browserbase.js';
import { SITES } from './config/sites.js';
import { BaseScraper } from './scrapers/BaseScraper.js';
import { Logger } from './utils/logger.js';

/**
 * Main application class
 */
class ScraperApp {
  constructor() {
    this.results = {};
  }

  /**
   * Run analysis for all sites
   */
  async runAllSites() {
    Logger.info('Starting analysis for all sites');
    
    const siteKeys = Object.keys(SITES);
    const results = {};

    for (const siteKey of siteKeys) {
      try {
        Logger.info(`\n${'='.repeat(50)}`);
        Logger.info(`Processing: ${SITES[siteKey].name}`);
        Logger.info(`${'='.repeat(50)}`);
        
        const scraper = new BaseScraper(SITES[siteKey]);
        const analysis = await scraper.runAnalysis();
        
        results[siteKey] = {
          success: true,
          analysis,
          timestamp: new Date().toISOString()
        };
        
        Logger.success(`✅ Completed: ${SITES[siteKey].name}`);
        
        // Add delay between sites to be respectful
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        Logger.error(`❌ Failed: ${SITES[siteKey].name}`, error);
        results[siteKey] = {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }

    return results;
  }

  /**
   * Run analysis for a specific site
   */
  async runSite(siteName) {
    const siteConfig = SITES[siteName];
    if (!siteConfig) {
      throw new Error(`Site '${siteName}' not found. Available sites: ${Object.keys(SITES).join(', ')}`);
    }

    Logger.info(`Running analysis for: ${siteConfig.name}`);
    
    const scraper = new BaseScraper(siteConfig);
    const analysis = await scraper.runAnalysis();
    
    return {
      success: true,
      analysis,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Display summary of results
   */
  displaySummary(results) {
    Logger.info('\n' + '='.repeat(60));
    Logger.info('ANALYSIS SUMMARY');
    Logger.info('='.repeat(60));

    const successful = Object.values(results).filter(r => r.success).length;
    const failed = Object.values(results).filter(r => !r.success).length;

    Logger.info(`Total sites: ${Object.keys(results).length}`);
    Logger.success(`Successful: ${successful}`);
    if (failed > 0) {
      Logger.error(`Failed: ${failed}`);
    }

    Logger.info('\nDetailed Results:');
    Object.entries(results).forEach(([site, result]) => {
      const status = result.success ? '✅' : '❌';
      const siteName = SITES[site]?.name || site;
      Logger.info(`${status} ${siteName}`);
      
      if (!result.success) {
        Logger.error(`   Error: ${result.error}`);
      }
    });

    Logger.info('\n📁 Check the following directories for detailed results:');
    Logger.info('   - screenshots/ (page screenshots)');
    Logger.info('   - results/ (detailed analysis data)');
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    // Validate configuration
    validateConfig();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const siteArg = args.find(arg => arg.startsWith('--site='));
    const specificSite = siteArg ? siteArg.split('=')[1] : null;

    const app = new ScraperApp();
    let results;

    if (specificSite) {
      // Run single site
      const result = await app.runSite(specificSite);
      results = { [specificSite]: result };
    } else {
      // Run all sites
      results = await app.runAllSites();
    }

    // Display summary
    app.displaySummary(results);

    Logger.success('\n🎉 Analysis complete!');
    
  } catch (error) {
    Logger.error('Application failed', error);
    process.exit(1);
  }
}

// Run the application
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
