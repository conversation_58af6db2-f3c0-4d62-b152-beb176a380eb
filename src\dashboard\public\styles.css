/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 700;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}



/* Sections */
.section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.section h2 i {
    color: #667eea;
    margin-right: 10px;
}

/* Sites Grid */
.sites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.site-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.site-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.site-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.site-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.site-info h3 {
    color: #2d3748;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.site-type {
    background: #edf2f7;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
}

.site-url {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 10px;
    word-break: break-all;
}

.site-categories {
    margin-bottom: 15px;
}

.site-categories small {
    color: #667eea;
    font-weight: 500;
}

.site-categories i {
    margin-right: 5px;
}

/* Jobs Container */
.jobs-container {
    min-height: 100px;
}

.no-jobs {
    text-align: center;
    color: #718096;
    padding: 40px;
}

.no-jobs i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.job-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.job-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.job-title {
    font-weight: 600;
    color: #2d3748;
}

.job-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-running {
    background: #bee3f8;
    color: #2b6cb0;
}

.status-completed {
    background: #c6f6d5;
    color: #276749;
}

.status-failed {
    background: #fed7d7;
    color: #c53030;
}

.status-starting {
    background: #faf089;
    color: #744210;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #edf2f7;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.job-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* Results Container */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.result-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.result-meta {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 10px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f7fafc;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
}

.modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Category Selection Modal */
.category-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f7fafc;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.category-item:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.category-item input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.category-item label {
    cursor: pointer;
    font-size: 0.9rem;
    color: #4a5568;
    margin: 0;
    flex: 1;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    flex-wrap: wrap;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.modal-actions .btn {
    min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .sites-grid {
        grid-template-columns: 1fr;
    }
    
    .job-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
