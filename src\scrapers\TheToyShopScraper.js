import { ProductScraper } from './ProductScraper.js';
import { Logger } from '../utils/logger.js';

/**
 * The Toy Shop-specific product scraper
 * Navigation: Homepage → Navigation Category → Subcategory → Products
 * Pagination: Traditional numbered pagination with Previous/Next buttons
 */
export class TheToyShopScraper extends ProductScraper {
  constructor(siteConfig) {
    super(siteConfig);
    this.categories = [
      { main: 'Type of Toy', sub: 'Action Figures' },
      { main: 'Type of Toy', sub: 'Building Blocks' },
      { main: 'Shop By Age', sub: '3-5 Years' },
      { main: 'Shop By Age', sub: '6-8 Years' },
      { main: 'Brands', sub: 'LEGO' },
      { main: 'Outdoor', sub: 'Garden Games' }
    ];
  }

  /**
   * Handle The Toy Shop cookie consent
   */
  async handleCookies() {
    try {
      Logger.info('Handling The Toy Shop cookie consent...');
      
      // Wait for cookie dialog to appear
      await this.stagehand.page.waitForTimeout(2000);
      
      // Look for and click "Accept All Cookies" button
      await this.stagehand.page.act('Click the "Accept All Cookies" button');
      
      // Wait for dialog to close
      await this.stagehand.page.waitForTimeout(1000);
      
      Logger.success('Cookie consent handled successfully');
    } catch (error) {
      Logger.warning('Cookie consent handling failed or not needed', error.message);
      // Continue anyway as cookies might not be required
    }
  }

  /**
   * Navigate to The Toy Shop category
   */
  async navigateToCategory(categoryObj = { main: 'Type of Toy', sub: 'Action Figures' }) {
    try {
      const { main, sub } = typeof categoryObj === 'string' 
        ? { main: 'Type of Toy', sub: categoryObj }
        : categoryObj;
        
      Logger.info(`Navigating to The Toy Shop category: ${main} > ${sub}`);
      
      // Click on main category (e.g., Type of Toy)
      await this.stagehand.page.act(`Click on "${main}" navigation link`);
      await this.stagehand.page.waitForTimeout(2000);
      
      // Click on subcategory (e.g., Action Figures)
      await this.stagehand.page.act(`Click on "${sub}" category link`);
      await this.stagehand.page.waitForTimeout(3000);
      
      Logger.success(`Successfully navigated to ${main} > ${sub}`);
    } catch (error) {
      Logger.error(`Failed to navigate to category: ${JSON.stringify(categoryObj)}`, error);
      throw error;
    }
  }

  /**
   * Extract products from current The Toy Shop page
   */
  async extractProducts() {
    try {
      Logger.info('Extracting products from The Toy Shop page...');
      
      const products = await this.stagehand.page.extract(`
        Extract all product information from the current page. For each toy product, get:
        - Product name
        - Price (in GBP)
        - Product description
        - Product image URL
        - Product page link
        - Age recommendation
        - Brand information
        - Availability status
        - Any special offers or discounts
        - Product rating if visible
        
        Return as an array of product objects with these fields:
        {
          "name": "product name",
          "price": "£X.XX",
          "description": "product description",
          "imageUrl": "image URL",
          "productUrl": "product page URL",
          "ageRange": "3+ years",
          "brand": "brand name",
          "available": true/false,
          "offers": ["offer text"],
          "rating": "4.5/5",
          "category": "current category"
        }
      `);
      
      if (products && Array.isArray(products)) {
        Logger.success(`Extracted ${products.length} products from The Toy Shop`);
        return products;
      } else {
        Logger.warning('No products extracted or invalid format');
        return [];
      }
    } catch (error) {
      Logger.error('Failed to extract products from The Toy Shop', error);
      return [];
    }
  }

  /**
   * Handle The Toy Shop pagination (numbered pages)
   */
  async handlePagination() {
    try {
      Logger.info('Checking for The Toy Shop pagination...');
      
      // First try to click "Next Page" link
      try {
        await this.stagehand.page.act('Click on "Next Page" link');
        Logger.success('Successfully clicked "Next Page" link');
        return true; // More content available
      } catch (error) {
        Logger.info('No "Next Page" link found, trying numbered pagination');
      }
      
      // Try to find next numbered page (e.g., page 2, 3, etc.)
      try {
        // Extract current page info to determine next page
        const pageInfo = await this.stagehand.page.extract(`
          Extract pagination information including current page number and available page links.
          Return as: { "currentPage": number, "totalPages": number, "hasNext": boolean }
        `);
        
        if (pageInfo && pageInfo.hasNext) {
          const nextPage = pageInfo.currentPage + 1;
          await this.stagehand.page.act(`Click on page ${nextPage} link`);
          Logger.success(`Successfully navigated to page ${nextPage}`);
          return true;
        } else {
          Logger.info('Reached last page according to pagination info');
          return false;
        }
      } catch (error) {
        Logger.info('Could not extract pagination info or navigate to next page');
        return false;
      }
      
    } catch (error) {
      Logger.info('Pagination ended or failed', error.message);
      return false; // No more content
    }
  }

  /**
   * Get available categories for The Toy Shop
   */
  getAvailableCategories() {
    return this.categories;
  }

  /**
   * Scrape specific The Toy Shop categories
   */
  async scrapeTheToyShop(selectedCategories = null) {
    const categoriesToScrape = selectedCategories || this.categories.slice(0, 2); // Default to first 2 categories
    
    Logger.info(`Starting The Toy Shop scraping for categories: ${categoriesToScrape.map(c => `${c.main}>${c.sub}`).join(', ')}`);
    
    return await this.scrapeProducts(categoriesToScrape);
  }

  /**
   * Quick scrape for testing (single category)
   */
  async quickScrape() {
    Logger.info('Starting The Toy Shop quick scrape (Type of Toy > Action Figures only)');
    return await this.scrapeProducts([{ main: 'Type of Toy', sub: 'Action Figures' }]);
  }
}
